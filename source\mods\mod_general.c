#include "mod_general.h"

void runModGeneral(s_modGeneral* dp)
{
	char i;
	int sumF1 = 0,sumF2 = 0;
	float dfdt1 = 0, dfdt2 = 0;
	#ifndef WIN32
	
//	if(*dp->Ua < 1*DEFAULT_DIV_VAL)
//	{
//		dp->F1_f = 50;
//		dp->F1 = 50*DEFAULT_DIV_VAL;
//	}
//	else
//	{
		dp->F1_f = g_FM1;	
		dp->F1 = g_FM1*DEFAULT_DIV_VAL;
//	}
	
	// 计算频率滑差 dfdt1
	dfdt1 = (dp->F1_f - dp->last_F1_f) * 200; // 5MS
	dp->last_F1_f = dp->F1_f; // 保存当前频率用于下次计算
	dp->dfdt1 = dfdt1;
	
//	if(*dp->UX < 1*DEFAULT_DIV_VAL)
//	{
//		dp->F2_f = 50;
//		dp->F2 = 50*DEFAULT_DIV_VAL;
//	}
//	else
//	{
		dp->F2_f = g_FM2;	
		dp->F2 = g_FM2*DEFAULT_DIV_VAL;
//	}
	
	// 计算频率滑差 dfdt2
	dfdt2 = (dp->F2_f - dp->last_F2_f) * 200; // 5MS
	dp->last_F2_f = dp->F2_f; // 保存当前频率用于下次计算
	dp->dfdt2 = dfdt2;
	
	#endif	
	
	//显示频率计算,需要求平均减少波动,频率值过零点时候才会变化，所以不能每次运行都计算
	dp->mod_run_cnt ++;
	if(dp->mod_run_cnt > 8)
	{
		dp->mod_run_cnt = 0;
		dp->F1_buf[dp->F_ptr] = dp->F1;
		dp->F2_buf[dp->F_ptr] = dp->F2;
		for(i = 0; i < F_BUF_LEN; i++)
		{
			sumF1 += dp->F1_buf[i];
			sumF2 += dp->F2_buf[i];
		}
		dp->F1_show = sumF1 / F_BUF_LEN;
		dp->F2_show = sumF2 / F_BUF_LEN;
		//dp->rate = dp->F1_show/50*(dp->Rated_Speed/10000);
		dp->rate_percent = dp->F1_show/50;
		dp->rate = (dp->Rated_Speed/10000)*dp->rate_percent;

		dp->F_ptr = circAft(dp->F_ptr, F_BUF_LEN, 1);
	}
	
	dp->calPointPtr = g_adBufPtr;
	
	return;
}

int initModGeneral(s_modGeneral* dp, char* dpName)
{
	dp->dpName = dpName;
	//注册开关量
    dp->const_0 = 0;
    dp->const_0_ana = 0;
	regBI(dpName, "bi_const0", 0 ,&dp->const_0);	//常数0
	regBI(dpName, "ovbi_rmt_strap", 0, &dp->rmt_strap);	
	regBI(dpName, "ovbi_rmt_grp", 0, &dp->rmt_grp);	
	regBI(dpName, "ovbi_rmt_set", 0, &dp->rmt_set);	
	
	//注册模拟量 
	regANA(dpName, "curSetNo",	0, &dp->curSetNo, 0, 0);
	regANA(dpName, "fm",	0, &dp->F1_show, DEFAULT_POINT, "Hz");//发电机频率fg
	regANA(dpName, "fx",	0, &dp->F2_show, DEFAULT_POINT, "Hz");//系统频率fx
	regANA(dpName, "rate",	0, &dp->rate, DEFAULT_POINT, 0);//发电机转速
	regANA(dpName, "rate_percent",	0, &dp->rate_percent, DEFAULT_POINT, 0);//转速百分比

    regANA(dpName, "const_0_ana",0, &dp->const_0_ana, DEFAULT_POINT, 0);

	//加入队列
	addTaskLevel2(runModGeneral, dp);
	return SUCC;
}

int initModParmGeneral(s_modGeneral* dp)
{
	dp->curSetNo = getSetByDesc("Settig_SecNo");
	dp->Rated_Speed = getSetByDesc("Rated_Speed");

	dp->rmt_strap = getSetByDesc("vbi_rmt_strap");	//远方投退压板
	dp->rmt_grp = getSetByDesc("vbi_rmt_grp");			//远方切区
	dp->rmt_set = getSetByDesc("vbi_rmt_set");			//远方修改定值
		
	//设置装置状态，平台用
	if(dp->rmt_strap)
		deviceState(DEV_RMT_STRAP);
	else
		deviceClearState(DEV_RMT_STRAP);
	
	if(dp->rmt_grp)
		deviceState(DEV_RMT_GRP);
	else
		deviceClearState(DEV_RMT_GRP);
	
	if(dp->rmt_set)
		deviceState(DEV_RMT_SET);
	else
		deviceClearState(DEV_RMT_SET);

	return SUCC;
}
