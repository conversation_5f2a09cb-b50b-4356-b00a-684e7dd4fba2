/**
  *****************************************************************************
  * @file    tcp_server2.c
  * <AUTHOR> @version 
  * @date    
  * @brief   第二个tcp服务端的实现
  *****************************************************************************
  */

#include "tcp_server.h"
#include "string.h"

/* 定义第二个TCP服务器的端口号 */
#define TCP_LOCAL_PORT2     2405  /* 第二个TCP服务器本地端口 */
#define TCP_REMOTE_PORT2    11    /* 第二个TCP服务器远端端口 */

extern ip4_addr_t ipaddr;

static TCP_RECBUF_ST tcp_rec_buf_st2;                      //第二个TCP服务器接收缓冲区
static P_TCP_RECBUF_ST p_tcp_rec_buf_st2 = &tcp_rec_buf_st2;

static char  g_clinet_stat2 = 0;                           //第二个TCP服务器客户端状态
static struct tcp_pcb *g_tpcb2;                            //第二个TCP服务器PCB

void combuf_data_font(unsigned char *buf, uint32_t blen, uint32_t num);

/******************************************************************************
 * 描述  : 第二个TCP服务器接收回调函数
 * 参数  : -
 * 返回  : -
******************************************************************************/
static err_t tcp_server2_recv(void *arg, struct tcp_pcb *tpcb,
                             struct pbuf *p, err_t err)
{
    if (p != NULL)
    {
        struct pbuf *ptmp = p;
        uint32_t xlen;
        
        while(ptmp != NULL)
        {                
            xlen =  p_tcp_rec_buf_st2->rev_len +  p->len;  
		    if (xlen > TCP_REC_BUF_LEN)
		    {
                if( p->len <= TCP_REC_BUF_LEN)
                {                                                                   
		    	     combuf_data_font( p_tcp_rec_buf_st2->rev_buf, TCP_REC_BUF_LEN, xlen - TCP_REC_BUF_LEN);
		    	     p_tcp_rec_buf_st2->rev_len -= (xlen - TCP_REC_BUF_LEN);
		    	     memcpy( p_tcp_rec_buf_st2->rev_buf +  p_tcp_rec_buf_st2->rev_len, (char *)p->payload,  p->len);
                     p_tcp_rec_buf_st2->rev_len +=  p->len;                    
                }
                else   //收到的数据，大于缓冲区长度，取最后的数据
                {
                    memcpy( p_tcp_rec_buf_st2->rev_buf , ((char *)p->payload + p->len - TCP_REC_BUF_LEN),  TCP_REC_BUF_LEN);
                    p_tcp_rec_buf_st2->rev_len =  TCP_REC_BUF_LEN;                                                
                }                                
                
		    }
		    else
		    {
		    	 memcpy( p_tcp_rec_buf_st2->rev_buf +  p_tcp_rec_buf_st2->rev_len, (char *)p->payload,  p->len);
                 p_tcp_rec_buf_st2->rev_len +=  p->len;
		    }		    
                        
            ptmp = p->next;
        }          
        
        /* 更新接收窗口 */
        tcp_recved(tpcb, p->tot_len);
        
        /* 释放缓冲区数据 */
        pbuf_free(p);
    }
    else if (err == ERR_OK)
    {
        g_clinet_stat2 = 0;
        tcp_recved(tpcb, p->tot_len);
        
        return tcp_close(tpcb);
    }

    return ERR_OK;
}

/******************************************************************************
 * 描述  : 第二个TCP服务器连接接受回调函数
 * 参数  : -
 * 返回  : -
******************************************************************************/
static err_t tcp_server2_accept(void *arg, struct tcp_pcb *newpcb, err_t err)
{
    LWIP_UNUSED_ARG(arg);
    LWIP_UNUSED_ARG(err);

    /* 设置接收回调函数 */
    tcp_setprio(newpcb, TCP_PRIO_MIN);
    
    /* 设置客户端状态为连接 */
    g_clinet_stat2 = 1;
    g_tpcb2 = newpcb;
    
    /* 注册接收回调函数 */
    tcp_recv(newpcb, tcp_server2_recv);

    return ERR_OK;
}

/******************************************************************************
 * 描述  : 创建第二个tcp服务器
 * 参数  : 无
 * 返回  : 无
******************************************************************************/
void tcp_server2_init(void)
{
    struct tcp_pcb *tpcb;

    memset(p_tcp_rec_buf_st2, 0, sizeof(tcp_rec_buf_st2)); //第二个TCP服务器内部数据接收区
    
    /* 创建tcp控制块 */
    tpcb = tcp_new();   
    g_tpcb2 = tpcb;
    
    if (tpcb != NULL)
    {
        err_t err;
        
        /* 绑定端口接收，接收对象为所有ip地址 */
        err = tcp_bind(tpcb, IP_ADDR_ANY, TCP_LOCAL_PORT2);

        if (err == ERR_OK)
        {
            /* 监听 */
            tpcb = tcp_listen(tpcb);

            /* 注册接入回调函数 */
            tcp_accept(tpcb, tcp_server2_accept);
            
            g_tpcb2 = tpcb;
            
            printf("tcp server2 listening\r\n");
            printf("tcp server2 ip:%d:%d:%d:%d port:%d\r\n",
                *((uint8_t *)&ipaddr.addr),
                *((uint8_t *)&ipaddr.addr + 1),
                *((uint8_t *)&ipaddr.addr + 2),
                *((uint8_t *)&ipaddr.addr + 3),
                tpcb->local_port);
        }
        else
        {
            memp_free(MEMP_TCP_PCB, tpcb);
            
            printf("can not bind pcb for tcp server2\r\n");
        }
    }
}

//获取第二个tcp服务器传来的数据
int get_tcp_server2_data(char *buf, int len)
{
    int rlen = 0;
        
    if ( p_tcp_rec_buf_st2->rev_len > 0)
	{
		if (p_tcp_rec_buf_st2->rev_len > len)
		{
			memcpy(buf, p_tcp_rec_buf_st2->rev_buf, len);
			combuf_data_font(p_tcp_rec_buf_st2->rev_buf, TCP_REC_BUF_LEN, len);
			p_tcp_rec_buf_st2->rev_len -= len;
			rlen = len;
		}
		else
		{
			memcpy(buf, p_tcp_rec_buf_st2->rev_buf, p_tcp_rec_buf_st2->rev_len);
			combuf_data_font( p_tcp_rec_buf_st2->rev_buf, p_tcp_rec_buf_st2->rev_len, p_tcp_rec_buf_st2->rev_len);
			rlen = p_tcp_rec_buf_st2->rev_len;
			p_tcp_rec_buf_st2->rev_len = 0;			
		}
	}	
	
	return rlen;    
}

//第二个tcp服务器发送数据
int send_tcp_server2_data(char *buff_tx, int buff_tx_len)
{
	 err_t err;
    if( g_clinet_stat2)
	{
		err = tcp_write(g_tpcb2, buff_tx,  buff_tx_len, 1);
		tcp_output(g_tpcb2);
		if(err == ERR_OK)
        return 0;
		else
				return -1;
	}
    else
        return -1;
}

void send_tcp_server2_data_plc(uint8_t *buff_tx, uint16_t buff_tx_len)
{
    send_tcp_server2_data((char *)buff_tx, (int)buff_tx_len);
}

/******************************** END OF FILE ********************************/
