#ifndef _COMMON_H_
#define _COMMON_H_

#include "string.h"
//#include "math.h"
#include "arm_math.h"
#include <stddef.h>

#define TIME_TEST		1

#define TASK_FUN_INT	(5)	//???????????????5ms

#define SRAM_NUM			1	//????????洢??
#define N103_MAX_NUM	2	//??????????????? ?????? (支持两个TCP服务器)
#define S103_MAX_NUM	1	//??????????????
#define LCD_NUM				1	//??????????

#define CLIENT				(N103_MAX_NUM + S103_MAX_NUM + SRAM_NUM + LCD_NUM)
#define N103_CLIENT_BASE	(0)
#define S103_CLIENT_BASE	(N103_CLIENT_BASE + N103_MAX_NUM)
#define SRAM_CLIENT_BASE	(S103_CLIENT_BASE + S103_MAX_NUM)
#define LCD_CLIENT_BASE		(SRAM_CLIENT_BASE + SRAM_NUM)

#define  FAIL 1		//???
#define  SUCC 0		//???

#define BOOT_VER_ADDR	0x8007F00		//boot?汾????????

#define MODBUS_TEST
//#define PLCTEST

#ifndef PI
#define PI 3.1415926
#endif

#ifdef WIN32

#include "stdio.h"
#include "winsock2.h"
#include "Windows.h"
#include <locale.h>
#pragma comment(lib,"ws2_32.lib")
#include "WinBase.h"
#include "process.h"

#else

#include <stdint.h>
//#include "stm32f4xx_hal.h"
//#include "xsj_lib.h"
#endif

/*???п???*/
typedef struct
{
	unsigned int writePtr;
	unsigned int readPtr[CLIENT];
}s_qCtl;

/*
?????????
*/

#define   RUNNING_OK			(0)			//???????????????
#define   INIT_NOT_OK			(1<<0)		//???δ????????
#define   REG_BI_FAIL			(1<<1)		//?????????????
#define   REG_ANA_FAIL			(1<<2)		//??????????????
#define   SETTING_CHAGE			(1<<3)		//????????
#define   AD_ADJ_INIT_OK		(1<<4)		//AD?????????

#define   EXCITATION_ERR	    (1<<5)		//?????????
#define   PLC_RUN_ERR			(1<<6)		//????????
#define   SETTING_ERR			(1<<7)		//?????????
#define   AD_ERR			    (1<<8)		//??????????????
#define   DO_ERR			    (1<<9)		//??????

/*
????????
*/

#define   DEV_NORMAL		(0)				//????
#define   DEV_CHK				(1<<0)		//??ü???
#define   DEV_RMT				(1<<1)		//??????
#define		DEV_N103			(1<<2)		//??????????
#define		DEV_S103			(1<<3)		//????????
#define		DEV_BCODE			(1<<4)		//??ò???B????
#define   DEV_RMT_STRAP (1<<5)		//?????????
#define   DEV_RMT_GRP 	(1<<6)		//???????
#define   DEV_RMT_SET   (1<<7)		//??????
#define   DEV_SPIFLASH_ERR		(1<<8)			//???SPI flash??????,????洢???

/*
???????
*/
typedef enum
{
	CN,								//????
	ENG,							//???
	LANGUES,
}e_langue;

/*
	???????????嶨??
*/
typedef struct
{
	char*     name_string;		//?????????
	char      name_len;			//?????????
}s_showName;

/*
	??????嶨??
*/
typedef struct
{
	char*				modName;					//?????
	char*				desc;						//???????????????
	s_showName			showName;					//?????????
	char*   			descSet;					//??????????
}s_name;

/*??????????*/
typedef struct
{
	unsigned short year;	//??
	unsigned short month;	//??
	unsigned short day;		//??
	unsigned short hour;	//?
	unsigned short min;		//??
	unsigned short s;		//??
	unsigned short ms;		//????
}s_date;

/*SOE??????嶨??*/
typedef struct
{
	unsigned short idx;		//??????
	unsigned short soeNo;	//SOE????
	s_date time;	//???
	char flg;		//???	(1->0 ???? 0->1 ????)
}s_soe;

typedef struct {
    int year;
    int month;
    int day;
    int app_ver1;
    int app_ver2;
    int plc_ver1;
    int plc_ver2;
    int app_crc;
} s_versionInfo;


extern e_langue g_langueSel;
extern unsigned int  g_bsFlg;
extern unsigned int  g_devState;
extern unsigned int  g_sys_100usCnt;
//extern unsigned int	 g_sys_ms;

#ifdef TIME_TEST
extern unsigned int taskMainTime;	
extern unsigned int taskSmplTime;	
extern unsigned int taskModTime;		
#endif

char getName(s_name* name, e_langue sel);
void deviceBs(unsigned int flg);
void deviceClearBs(unsigned int flg);
void deviceState(unsigned int flg);
void deviceClearState(unsigned int flg);
unsigned short CRC16(unsigned char *src, unsigned int datalen);
char getOneBit(unsigned char* start, int pos);
void setOneBit(unsigned char* start, int pos);
unsigned int  getIntTime(unsigned int lastCnt);
unsigned short allocFlashSpace(unsigned short size);
int retSetVal(int val, char diffPoint);
void devSgl_reg(void);
void deviceSgl_task(void);

int initModTask(void);
int initModParm(void);
#endif
