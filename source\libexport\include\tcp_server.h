

#ifndef __TCP_SERVER_H__
#define __TCP_SERVER_H__


#ifdef __cplusplus
extern "C"{
#endif


#include "main.h"
#include "lwip.h"
#include "tcp.h"

#define TCP_REC_BUF_LEN  512
    
typedef struct
{
    uint32_t rev_len;
    uint8_t  rev_buf[ TCP_REC_BUF_LEN ];
} TCP_RECBUF_ST, *P_TCP_RECBUF_ST;
    
static err_t tcp_server_recv(void *arg, struct tcp_pcb *tpcb,
                             struct pbuf *p, err_t err);
static err_t tcp_server_accept(void *arg, struct tcp_pcb *newpcb, err_t err);
void tcp_server_init(void);

int get_tcp_server_data(char *buf, int len);
int send_tcp_server_data(char *buff_tx, int buff_tx_len);
void send_tcp_server_data_plc(uint8_t *buff_tx, uint16_t buff_tx_len);

// 第二个TCP服务器接口
void tcp_server2_init(void);
int get_tcp_server2_data(char *buf, int len);
int send_tcp_server2_data(char *buff_tx, int buff_tx_len);
void send_tcp_server2_data_plc(uint8_t *buff_tx, uint16_t buff_tx_len);

#ifdef __cplusplus
}
#endif

#endif
