#include "plc_reftable.h"

// 全局变量定义 - PLC数字量输出状态标志
unsigned char g_plc_output_active = 0;

char g_plc_ctl[64];

// PLC引用表定义 - 完全参照103协议的引用表设计，结合PLC库的地址映射
s_plcRefTab g_BIplcRefTab[] = {
    //!!! 开入映射关系 %IX区域 (保护->PLC)
    {.desc = "modBi.bi_in01", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 0, .plcAddrB = 0 },
    {.desc = "modBi.bi_in02", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 0, .plcAddrB = 1 },
    {.desc = "modBi.bi_in03", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 0, .plcAddrB = 2 },
    {.desc = "modBi.bi_in04", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 0, .plcAddrB = 3 },
    {.desc = "modBi.bi_in05", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 0, .plcAddrB = 4 },
    {.desc = "modBi.bi_in06", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 0, .plcAddrB = 5 },
    {.desc = "modBi.bi_in07", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 0, .plcAddrB = 6 },
    {.desc = "modBi.bi_in08", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 0, .plcAddrB = 7 },
    {.desc = "modBi.bi_in09", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 1, .plcAddrB = 0 },
    {.desc = "modBi.bi_in10", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 1, .plcAddrB = 1 },
    {.desc = "modBi.bi_in11", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 1, .plcAddrB = 2 },
    {.desc = "modBi.bi_in12", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 1, .plcAddrB = 3 },
    {.desc = "modBi.bi_in13", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 1, .plcAddrB = 4 },
    {.desc = "modBi.bi_in14", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 1, .plcAddrB = 5 },
    {.desc = "modBi.bi_in15", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 1, .plcAddrB = 6 },
    {.desc = "modBi.bi_in16", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 1, .plcAddrB = 7 },
    {.desc = "modBi.bi_in17", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 2, .plcAddrB = 0 },
    {.desc = "modBi.bi_in18", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 2, .plcAddrB = 1 },
    {.desc = "modBi.bi_in19", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 2, .plcAddrB = 2 },
    {.desc = "modBi.bi_in20", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 2, .plcAddrB = 3 },
    {.desc = "modBi.bi_in21", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 2, .plcAddrB = 4 },
    {.desc = "modBi.bi_in22", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 2, .plcAddrB = 5 },
    {.desc = "modBi.bi_in23", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 2, .plcAddrB = 6 },
    {.desc = "modBi.bi_in24", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 2, .plcAddrB = 7 },
    {.desc = "modBi.bi_in25", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 3, .plcAddrB = 0 },
    {.desc = "modBi.bi_in26", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 3, .plcAddrB = 1 },
    {.desc = "modBi.bi_in27", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 3, .plcAddrB = 2 },
    {.desc = "modBi.bi_in28", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 3, .plcAddrB = 3 },
    {.desc = "modBi.bi_in29", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 3, .plcAddrB = 4 },
    {.desc = "modBi.bi_in30", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 3, .plcAddrB = 5 },
//    {.desc = "modGeneral.bi_const0", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 3, .plcAddrB = 6 },//TODO，可能会导致异常
//    {.desc = "modGeneral.bi_const0", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I',.plcAddrA = 3, .plcAddrB = 7 },

    //!!! 开出映射关系 %QX区域 (PLC->保护)
    {.desc = "modBo.bi_out01", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 0, .plcAddrB = 0},
    {.desc = "modBo.bi_out02", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 0, .plcAddrB = 1},
    {.desc = "modBo.bi_out03", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 0, .plcAddrB = 2},
    {.desc = "modBo.bi_out04", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 0, .plcAddrB = 3},
    {.desc = "modBo.bi_out05", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 0, .plcAddrB = 4},
    {.desc = "modBo.bi_out06", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 0, .plcAddrB = 5},
    {.desc = "modBo.bi_out07", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 0, .plcAddrB = 6},
    {.desc = "modBo.bi_out08", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 0, .plcAddrB = 7},
    {.desc = "modBo.bi_out09", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 1, .plcAddrB = 0},
    {.desc = "modBo.bi_out10", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 1, .plcAddrB = 1},
    {.desc = "modBo.bi_out11", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 1, .plcAddrB = 2},
    {.desc = "modBo.bi_out12", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 1, .plcAddrB = 3},
    {.desc = "modBo.bi_out13", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 1, .plcAddrB = 4},
    {.desc = "modBo.bi_out14", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 1, .plcAddrB = 5},
    {.desc = "modBo.bi_out15", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 1, .plcAddrB = 6},
    {.desc = "modBo.bi_out16", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 1, .plcAddrB = 7},
    {.desc = "modBo.bi_out17", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 2, .plcAddrB = 0},
    {.desc = "modBo.bi_out18", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 2, .plcAddrB = 1},
    {.desc = "modBo.bi_out19", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 2, .plcAddrB = 2},
    {.desc = "modBo.bi_out20", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 2, .plcAddrB = 3},
    {.desc = "modBo.bi_out21", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 2, .plcAddrB = 4},
    {.desc = "modBo.bi_out22", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 2, .plcAddrB = 5},
    {.desc = "modBo.bi_out23", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 2, .plcAddrB = 6},
    {.desc = "modBo.bi_out24", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 2, .plcAddrB = 7},
    {.desc = "modBo.bi_out25", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 3, .plcAddrB = 0},
    {.desc = "modBo.bi_out26", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 3, .plcAddrB = 1},
    {.desc = "modBo.bi_out27", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 3, .plcAddrB = 2},
    {.desc = "modBo.bi_out28", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 3, .plcAddrB = 3},
    {.desc = "modBo.bi_out29", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 3, .plcAddrB = 4},
    {.desc = "modBo.bi_out30", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 3, .plcAddrB = 5},
    {.desc = "modBo.bi_out31", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 3, .plcAddrB = 6},//???
    {.desc = "modBo.bi_out32", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 3, .plcAddrB = 7},//???
    {.desc = "modBo.bi_out33", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 4, .plcAddrB = 0},//???
//    {.desc = "modGeneral.bi_const0", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 4, .plcAddrB = 1},//TODO，可能会导致异常
//    {.desc = "modGeneral.bi_const0", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 4, .plcAddrB = 2},

    // 中间变量映射关系 %MX区域 (双向)
    {.desc = "plc_var_001", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 0, .plcAddrB = 0},
    {.desc = "plc_var_002", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 0, .plcAddrB = 1},
    {.desc = "plc_var_003", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 0, .plcAddrB = 2},
    {.desc = "plc_var_004", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 0, .plcAddrB = 3},
    {.desc = "plc_var_005", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 0, .plcAddrB = 4},
    {.desc = "plc_var_006", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 0, .plcAddrB = 5},
    {.desc = "plc_var_007", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 0, .plcAddrB = 6},
    {.desc = "plc_var_008", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 0, .plcAddrB = 7},
    {.desc = "plc_var_009", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 1, .plcAddrB = 0},
    {.desc = "plc_var_010", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 1, .plcAddrB = 1},
    {.desc = "plc_var_011", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 1, .plcAddrB = 2},
    {.desc = "plc_var_012", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 1, .plcAddrB = 3},
    {.desc = "plc_var_013", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 1, .plcAddrB = 4},
    {.desc = "plc_var_014", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 1, .plcAddrB = 5},
    {.desc = "plc_var_015", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 1, .plcAddrB = 6},
    {.desc = "plc_var_016", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 1, .plcAddrB = 7},
    {.desc = "plc_var_017", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 2, .plcAddrB = 0},
    {.desc = "plc_var_018", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 2, .plcAddrB = 1},
    {.desc = "plc_var_019", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 2, .plcAddrB = 2},
    {.desc = "plc_var_020", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 2, .plcAddrB = 3},
    {.desc = "plc_var_021", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 2, .plcAddrB = 4},
    {.desc = "plc_var_022", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 2, .plcAddrB = 5},
    {.desc = "plc_var_023", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 2, .plcAddrB = 6},
    {.desc = "plc_var_024", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 2, .plcAddrB = 7},
    {.desc = "plc_var_025", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 3, .plcAddrB = 0},
    {.desc = "plc_var_026", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 3, .plcAddrB = 1},
    {.desc = "plc_var_027", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 3, .plcAddrB = 2},
    {.desc = "plc_var_028", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 3, .plcAddrB = 3},
    {.desc = "plc_var_029", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 3, .plcAddrB = 4},
    {.desc = "plc_var_030", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 3, .plcAddrB = 5},
    {.desc = "plc_var_031", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 3, .plcAddrB = 6},
    {.desc = "plc_var_032", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 3, .plcAddrB = 7},
    {.desc = "plc_var_033", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 4, .plcAddrB = 0},
    {.desc = "plc_var_034", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 4, .plcAddrB = 1},
    {.desc = "plc_var_035", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 4, .plcAddrB = 2},
    {.desc = "plc_var_036", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 4, .plcAddrB = 3},
    {.desc = "plc_var_037", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 4, .plcAddrB = 4},
    {.desc = "plc_var_038", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 4, .plcAddrB = 5},
    {.desc = "plc_var_039", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 4, .plcAddrB = 6},
    {.desc = "plc_var_040", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 4, .plcAddrB = 7},  
    {.desc = "plc_var_041", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 5, .plcAddrB = 0},
    {.desc = "plc_var_042", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 5, .plcAddrB = 1},
    {.desc = "plc_var_043", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 5, .plcAddrB = 2},
    {.desc = "plc_var_044", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 5, .plcAddrB = 3},
    {.desc = "plc_var_045", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 5, .plcAddrB = 4},
    {.desc = "plc_var_046", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 5, .plcAddrB = 5},
    {.desc = "plc_var_047", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 5, .plcAddrB = 6},
    {.desc = "plc_var_048", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 5, .plcAddrB = 7},
    {.desc = "plc_var_049", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 6, .plcAddrB = 0},
    {.desc = "plc_var_050", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 6, .plcAddrB = 1},
    {.desc = "plc_var_051", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 6, .plcAddrB = 2},
    {.desc = "plc_var_052", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 6, .plcAddrB = 3},
    {.desc = "plc_var_053", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 6, .plcAddrB = 4},
    {.desc = "plc_var_054", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 6, .plcAddrB = 5},
    {.desc = "plc_var_055", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 6, .plcAddrB = 6},
    {.desc = "plc_var_056", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 6, .plcAddrB = 7},
    {.desc = "plc_var_057", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 7, .plcAddrB = 0},
    {.desc = "plc_var_058", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 7, .plcAddrB = 1},
    {.desc = "plc_var_059", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 7, .plcAddrB = 2},
    {.desc = "plc_var_060", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 7, .plcAddrB = 3},
    {.desc = "plc_var_061", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 7, .plcAddrB = 4},
    {.desc = "plc_var_062", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 7, .plcAddrB = 5},
    {.desc = "plc_var_063", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 7, .plcAddrB = 6},
    {.desc = "plc_var_064", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 7, .plcAddrB = 7},

//    {.desc = "plc_var2_001", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 8, .plcAddrB = 0},
//    {.desc = "plc_var2_002", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 8, .plcAddrB = 1},
//    {.desc = "plc_var2_003", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 8, .plcAddrB = 2},
//    {.desc = "plc_var2_004", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 8, .plcAddrB = 3},
//    {.desc = "plc_var2_005", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 8, .plcAddrB = 4},
//    {.desc = "plc_var2_006", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 8, .plcAddrB = 5},
//    {.desc = "plc_var2_007", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 8, .plcAddrB = 6},
//    {.desc = "plc_var2_008", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 8, .plcAddrB = 7},
//    {.desc = "plc_var2_009", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 9, .plcAddrB = 0},
//    {.desc = "plc_var2_010", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 9, .plcAddrB = 1},
//    {.desc = "plc_var2_011", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 9, .plcAddrB = 2},
//    {.desc = "plc_var2_012", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 9, .plcAddrB = 3},
//    {.desc = "plc_var2_013", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 9, .plcAddrB = 4},
//    {.desc = "plc_var2_014", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 9, .plcAddrB = 5},
//    {.desc = "plc_var2_015", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 9, .plcAddrB = 6},
//    {.desc = "plc_var2_016", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 9, .plcAddrB = 7},
//    {.desc = "plc_var2_017", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 10, .plcAddrB = 0},
//    {.desc = "plc_var2_018", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 10, .plcAddrB = 1},
//    {.desc = "plc_var2_019", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 10, .plcAddrB = 2},
//    {.desc = "plc_var2_020", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 10, .plcAddrB = 3},
//    {.desc = "plc_var2_021", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 10, .plcAddrB = 4},
//    {.desc = "plc_var2_022", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 10, .plcAddrB = 5},
//    {.desc = "plc_var2_023", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 10, .plcAddrB = 6},
//    {.desc = "plc_var2_024", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 10, .plcAddrB = 7},
//    {.desc = "plc_var2_025", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 11, .plcAddrB = 0},
//    {.desc = "plc_var2_026", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 11, .plcAddrB = 1},
//    {.desc = "plc_var2_027", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 11, .plcAddrB = 2},
//    {.desc = "plc_var2_028", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 11, .plcAddrB = 3},
//    {.desc = "plc_var2_029", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 11, .plcAddrB = 4},
//    {.desc = "plc_var2_030", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 11, .plcAddrB = 5},
//    {.desc = "plc_var2_031", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 11, .plcAddrB = 6},
//    {.desc = "plc_var2_032", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 11, .plcAddrB = 7},
//    {.desc = "plc_var2_033", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 12, .plcAddrB = 0},
//    {.desc = "plc_var2_034", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 12, .plcAddrB = 1},
//    {.desc = "plc_var2_035", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 12, .plcAddrB = 2},
//    {.desc = "plc_var2_036", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 12, .plcAddrB = 3},
//    {.desc = "plc_var2_037", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 12, .plcAddrB = 4},
//    {.desc = "plc_var2_038", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 12, .plcAddrB = 5},
//    {.desc = "plc_var2_039", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 12, .plcAddrB = 6},
//    {.desc = "plc_var2_040", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 12, .plcAddrB = 7},
//    {.desc = "plc_var2_041", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 13, .plcAddrB = 0},
//    {.desc = "plc_var2_042", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 13, .plcAddrB = 1},
//    {.desc = "plc_var2_043", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 13, .plcAddrB = 2},
//    {.desc = "plc_var2_044", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 13, .plcAddrB = 3},
//    {.desc = "plc_var2_045", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 13, .plcAddrB = 4},
//    {.desc = "plc_var2_046", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 13, .plcAddrB = 5},
//    {.desc = "plc_var2_047", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 13, .plcAddrB = 6},
//    {.desc = "plc_var2_048", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 13, .plcAddrB = 7},
//    {.desc = "plc_var2_049", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 14, .plcAddrB = 0},
//    {.desc = "plc_var2_050", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 14, .plcAddrB = 1},
//    {.desc = "plc_var2_051", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 14, .plcAddrB = 2},
//    {.desc = "plc_var2_052", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 14, .plcAddrB = 3},
//    {.desc = "plc_var2_053", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 14, .plcAddrB = 4},
//    {.desc = "plc_var2_054", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 14, .plcAddrB = 5},
//    {.desc = "plc_var2_055", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 14, .plcAddrB = 6},
//    {.desc = "plc_var2_056", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 14, .plcAddrB = 7},
//    {.desc = "plc_var2_057", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 15, .plcAddrB = 0},
//    {.desc = "plc_var2_058", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 15, .plcAddrB = 1},
//    {.desc = "plc_var2_059", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 15, .plcAddrB = 2},
//    {.desc = "plc_var2_060", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 15, .plcAddrB = 3},
//    {.desc = "plc_var2_061", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 15, .plcAddrB = 4},
//    {.desc = "plc_var2_062", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 15, .plcAddrB = 5},
//    {.desc = "plc_var2_063", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 15, .plcAddrB = 6},
//    {.desc = "plc_var2_064", .plcIoType = PLC_MIDDLE, .plcVarTypeChar = 'M',.plcAddrA = 15, .plcAddrB = 7}

};

s_plcRefTab g_ANAplcRefTab[] = {
    //模入映射关系 %ID区域 (保护->PLC)
    {.desc = "modGeneral.fm", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I', .plcAddrA = 0},
    {.desc = "modGeneral.fx", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I', .plcAddrA = 1},
    {.desc = "modVol.ua",     .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I', .plcAddrA = 2},
    {.desc = "modVol.ub",     .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I', .plcAddrA = 3},
    {.desc = "modVol.uc",     .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I', .plcAddrA = 4},
    {.desc = "modVol.ux",     .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I', .plcAddrA = 5},
    {.desc = "modVol.uab",    .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I', .plcAddrA = 6},
    {.desc = "modVol.ubc",    .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I', .plcAddrA = 7},
    {.desc = "modVol.uca",    .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I', .plcAddrA = 8},
    {.desc = "modMeasData.P", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I', .plcAddrA = 9},
    {.desc = "modMeasData.Q", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I', .plcAddrA = 10},
    {.desc = "modMeasData.cos", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I', .plcAddrA = 11},
    {.desc = "modGeneral.const_0_ana", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I', .plcAddrA = 12},//TODO 直流
    {.desc = "modGeneral.const_0_ana", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I', .plcAddrA = 13},
    {.desc = "modGeneral.const_0_ana", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I', .plcAddrA = 14},
    {.desc = "modGeneral.const_0_ana", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I', .plcAddrA = 15},
    {.desc = "modGeneral.rate", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I', .plcAddrA = 16},//!!! 转速
    {.desc = "modGeneral.const_0_ana", .plcIoType = PLC_INPUT, .plcVarTypeChar = 'I', .plcAddrA = 17}
#if 0
    // 模出映射关系 %QD区域 (PLC->保护)
    {.desc = "modGeneral.bi_const0", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 0, .plcAddrB = 0},
    {.desc = "modGeneral.bi_const0", .plcIoType = PLC_OUTPUT, .plcVarTypeChar = 'Q',.plcAddrA = 1, .plcAddrB = 0},
#endif
};

s_plcSetRefTab g_allSetplcRefTab[] = {
    {.desc = "plc_dz_001"},
    {.desc = "plc_dz_002"},
    {.desc = "plc_dz_003"},
    {.desc = "plc_dz_004"},
    {.desc = "plc_dz_005"},
    {.desc = "plc_dz_006"},
    {.desc = "plc_dz_007"},
    {.desc = "plc_dz_008"},
    {.desc = "plc_dz_009"},
    {.desc = "plc_dz_010"},
    {.desc = "plc_dz_011"},
    {.desc = "plc_dz_012"},
    {.desc = "plc_dz_013"},
    {.desc = "plc_dz_014"},
    {.desc = "plc_dz_015"},
    {.desc = "plc_dz_016"},
    {.desc = "plc_dz_017"},
    {.desc = "plc_dz_018"},
    {.desc = "plc_dz_019"},
    {.desc = "plc_dz_020"},
    {.desc = "plc_dz_021"},
    {.desc = "plc_dz_022"},
    {.desc = "plc_dz_023"},
    {.desc = "plc_dz_024"},
    {.desc = "plc_dz_025"},
    {.desc = "plc_dz_026"},
    {.desc = "plc_dz_027"},
    {.desc = "plc_dz_028"},
    {.desc = "plc_dz_029"},
    {.desc = "plc_dz_030"},
    {.desc = "plc_dz_031"},
    {.desc = "plc_dz_032"},
    {.desc = "plc_dz_033"},
    {.desc = "plc_dz_034"},
    {.desc = "plc_dz_035"},
    {.desc = "plc_dz_036"},
    {.desc = "plc_dz_037"},
    {.desc = "plc_dz_038"},
    {.desc = "plc_dz_039"},
    {.desc = "plc_dz_040"},
    {.desc = "plc_dz_041"},
    {.desc = "plc_dz_042"},
    {.desc = "plc_dz_043"},
    {.desc = "plc_dz_044"},
    {.desc = "plc_dz_045"},
    {.desc = "plc_dz_046"},
    {.desc = "plc_dz_047"},
    {.desc = "plc_dz_048"},
    {.desc = "plc_dz_049"},
    {.desc = "plc_dz_050"},
    {.desc = "plc_dz_051"},
    {.desc = "plc_dz_052"},
    {.desc = "plc_dz_053"},
    {.desc = "plc_dz_054"},
    {.desc = "plc_dz_055"},
    {.desc = "plc_dz_056"},
    {.desc = "plc_dz_057"},
    {.desc = "plc_dz_058"},
    {.desc = "plc_dz_059"},
    {.desc = "plc_dz_060"},
    {.desc = "plc_dz_061"},
    {.desc = "plc_dz_062"},
    {.desc = "plc_dz_063"},
    {.desc = "plc_dz_064"},
    {.desc = "plc_dz_065"},
    {.desc = "plc_dz_066"},
    {.desc = "plc_dz_067"},
    {.desc = "plc_dz_068"},
    {.desc = "plc_dz_069"},
    {.desc = "plc_dz_070"},
    {.desc = "plc_dz_071"},
    {.desc = "plc_dz_072"},
    {.desc = "plc_dz_073"},
    {.desc = "plc_dz_074"},
    {.desc = "plc_dz_075"},
    {.desc = "plc_dz_076"},
    {.desc = "plc_dz_077"},
    {.desc = "plc_dz_078"},
    {.desc = "plc_dz_079"},
    {.desc = "plc_dz_080"},
    {.desc = "plc_dz_081"},
    {.desc = "plc_dz_082"},
    {.desc = "plc_dz_083"},
    {.desc = "plc_dz_084"},
    {.desc = "plc_dz_085"},
    {.desc = "plc_dz_086"},
    {.desc = "plc_dz_087"},
    {.desc = "plc_dz_088"},
    {.desc = "plc_dz_089"},
    {.desc = "plc_dz_090"},
    {.desc = "plc_dz_091"},
    {.desc = "plc_dz_092"},
    {.desc = "plc_dz_093"},
    {.desc = "plc_dz_094"},
    {.desc = "plc_dz_095"},
    {.desc = "plc_dz_096"},
    {.desc = "plc_dz_097"},
    {.desc = "plc_dz_098"},
    {.desc = "plc_dz_099"},
    {.desc = "plc_dz_100"},
    {.desc = "plc_dz_101"},
    {.desc = "plc_dz_102"},
    {.desc = "plc_dz_103"},
    {.desc = "plc_dz_104"},
    {.desc = "plc_dz_105"},
    {.desc = "plc_dz_106"},
    {.desc = "plc_dz_107"},
    {.desc = "plc_dz_108"},
    {.desc = "plc_dz_109"},
    {.desc = "plc_dz_110"},
    {.desc = "plc_dz_111"},
    {.desc = "plc_dz_112"},
    {.desc = "plc_dz_113"},
    {.desc = "plc_dz_114"},
    {.desc = "plc_dz_115"},
    {.desc = "plc_dz_116"},
    {.desc = "plc_dz_117"},
    {.desc = "plc_dz_118"},
    {.desc = "plc_dz_119"},
    {.desc = "plc_dz_120"},
    {.desc = "plc_dz_121"},
    {.desc = "plc_dz_122"},
    {.desc = "plc_dz_123"},
    {.desc = "plc_dz_124"},
    {.desc = "plc_dz_125"},
    {.desc = "plc_dz_126"},
    {.desc = "plc_dz_127"},
    {.desc = "plc_dz_128"}
};

s_plcvarRefTab g_plc_var2RefTab[] =
{
    { .desc = "plc_var2_001"          },
    { .desc = "plc_var2_002"          },
    { .desc = "plc_var2_003"          },
    { .desc = "plc_var2_004"          },
    { .desc = "plc_var2_005"          },
    { .desc = "plc_var2_006"          },
    { .desc = "plc_var2_007"          },
    { .desc = "plc_var2_008"          },
    { .desc = "plc_var2_009"          },
    { .desc = "plc_var2_010"          },
    { .desc = "plc_var2_011"          },
    { .desc = "plc_var2_012"          },
    { .desc = "plc_var2_013"          },
    { .desc = "plc_var2_014"          },
    { .desc = "plc_var2_015"          },
    { .desc = "plc_var2_016"          },
    { .desc = "plc_var2_017"          },
    { .desc = "plc_var2_018"          },
    { .desc = "plc_var2_019"          },
    { .desc = "plc_var2_020"          },
    { .desc = "plc_var2_021"          },
    { .desc = "plc_var2_022"          },
    { .desc = "plc_var2_023"          },
    { .desc = "plc_var2_024"          },
    { .desc = "plc_var2_025"          },
    { .desc = "plc_var2_026"          },
    { .desc = "plc_var2_027"          },
    { .desc = "plc_var2_028"          },
    { .desc = "plc_var2_029"          },
    { .desc = "plc_var2_030"          },
    { .desc = "plc_var2_031"          },
    { .desc = "plc_var2_032"          },
    { .desc = "plc_var2_033"          },
    { .desc = "plc_var2_034"          },
    { .desc = "plc_var2_035"          },
    { .desc = "plc_var2_036"          },
    { .desc = "plc_var2_037"          },
    { .desc = "plc_var2_038"          },
    { .desc = "plc_var2_039"          },
    { .desc = "plc_var2_040"          },
    { .desc = "plc_var2_041"          },
    { .desc = "plc_var2_042"          },
    { .desc = "plc_var2_043"          },
    { .desc = "plc_var2_044"          },
    { .desc = "plc_var2_045"          },
    { .desc = "plc_var2_046"          },
    { .desc = "plc_var2_047"          },
    { .desc = "plc_var2_048"          },
    { .desc = "plc_var2_049"          },
    { .desc = "plc_var2_050"          },
    { .desc = "plc_var2_051"          },
    { .desc = "plc_var2_052"          },
    { .desc = "plc_var2_053"          },
    { .desc = "plc_var2_054"          },
    { .desc = "plc_var2_055"          },
    { .desc = "plc_var2_056"          },
    { .desc = "plc_var2_057"          },
    { .desc = "plc_var2_058"          },
    { .desc = "plc_var2_059"          },
    { .desc = "plc_var2_060"          },
    { .desc = "plc_var2_061"          },
    { .desc = "plc_var2_062"          },
    { .desc = "plc_var2_063"          },
    { .desc = "plc_var2_064"          },
    { .desc = "plc_var2_065"          },
    { .desc = "plc_var2_066"          },
    { .desc = "plc_var2_067"          },
    { .desc = "plc_var2_068"          },
    { .desc = "plc_var2_069"          },
    { .desc = "plc_var2_070"          },
    { .desc = "plc_var2_071"          },
    { .desc = "plc_var2_072"          },
    { .desc = "plc_var2_073"          },
    { .desc = "plc_var2_074"          },
    { .desc = "plc_var2_075"          },
    { .desc = "plc_var2_076"          },
    { .desc = "plc_var2_077"          },
    { .desc = "plc_var2_078"          },
    { .desc = "plc_var2_079"          },
    { .desc = "plc_var2_080"          },
    { .desc = "plc_var2_081"          },
    { .desc = "plc_var2_082"          },
    { .desc = "plc_var2_083"          },
    { .desc = "plc_var2_084"          },
    { .desc = "plc_var2_085"          },
    { .desc = "plc_var2_086"          },
    { .desc = "plc_var2_087"          },
    { .desc = "plc_var2_088"          },
    { .desc = "plc_var2_089"          },
    { .desc = "plc_var2_090"          },
    { .desc = "plc_var2_091"          },
    { .desc = "plc_var2_092"          },
    { .desc = "plc_var2_093"          },
    { .desc = "plc_var2_094"          },
    { .desc = "plc_var2_095"          },
    { .desc = "plc_var2_096"          },
    { .desc = "plc_var2_097"          },
    { .desc = "plc_var2_098"          },
    { .desc = "plc_var2_099"          },
    { .desc = "plc_var2_100"          },
    { .desc = "plc_var2_101"          },
    { .desc = "plc_var2_102"          },
    { .desc = "plc_var2_103"          },
    { .desc = "plc_var2_104"          },
    { .desc = "plc_var2_105"          },
    { .desc = "plc_var2_106"          },
    { .desc = "plc_var2_107"          },
    { .desc = "plc_var2_108"          },
    { .desc = "plc_var2_109"          },
    { .desc = "plc_var2_110"          },
    { .desc = "plc_var2_111"          },
    { .desc = "plc_var2_112"          },
    { .desc = "plc_var2_113"          },
    { .desc = "plc_var2_114"          },
    { .desc = "plc_var2_115"          },
    { .desc = "plc_var2_116"          },
    { .desc = "plc_var2_117"          },
    { .desc = "plc_var2_118"          },
    { .desc = "plc_var2_119"          },
    { .desc = "plc_var2_120"          },
    { .desc = "plc_var2_121"          },
    { .desc = "plc_var2_122"          },
    { .desc = "plc_var2_123"          },
    { .desc = "plc_var2_124"          },
    { .desc = "plc_var2_125"          },
    { .desc = "plc_var2_126"          },
    { .desc = "plc_var2_127"          },
    { .desc = "plc_var2_128"          }
};

s_plcsoeRefTabItem g_plcsoeRefTab[] = {
    {.desc = "plc_soe_001"},
    {.desc = "plc_soe_002"},
    {.desc = "plc_soe_003"},
    {.desc = "plc_soe_004"},
    {.desc = "plc_soe_005"},
    {.desc = "plc_soe_006"},
    {.desc = "plc_soe_007"},
    {.desc = "plc_soe_008"},
    {.desc = "plc_soe_009"},
    {.desc = "plc_soe_010"},
    {.desc = "plc_soe_011"},
    {.desc = "plc_soe_012"},
    {.desc = "plc_soe_013"},
    {.desc = "plc_soe_014"},
    {.desc = "plc_soe_015"},
    {.desc = "plc_soe_016"},
    {.desc = "plc_soe_017"},
    {.desc = "plc_soe_018"},
    {.desc = "plc_soe_019"},
    {.desc = "plc_soe_020"},
    {.desc = "plc_soe_021"},
    {.desc = "plc_soe_022"},
    {.desc = "plc_soe_023"},
    {.desc = "plc_soe_024"},
    {.desc = "plc_soe_025"},
    {.desc = "plc_soe_026"},
    {.desc = "plc_soe_027"},
    {.desc = "plc_soe_028"},
//    {.desc = "plc_soe_029"},
//    {.desc = "plc_soe_030"},
//    {.desc = "plc_soe_031"},
//    {.desc = "plc_soe_032"},
//    {.desc = "plc_soe_033"},
//    {.desc = "plc_soe_034"},
//    {.desc = "plc_soe_035"},
//    {.desc = "plc_soe_036"},
//    {.desc = "plc_soe_037"},
//    {.desc = "plc_soe_038"},
//    {.desc = "plc_soe_039"},
//    {.desc = "plc_soe_040"},
//    {.desc = "plc_soe_041"},
//    {.desc = "plc_soe_042"},
//    {.desc = "plc_soe_043"},
//    {.desc = "plc_soe_044"},
//    {.desc = "plc_soe_045"},
//    {.desc = "plc_soe_046"},
//    {.desc = "plc_soe_047"},
//    {.desc = "plc_soe_048"},
//    {.desc = "plc_soe_049"},
//    {.desc = "plc_soe_050"},
//    {.desc = "plc_soe_051"},
//    {.desc = "plc_soe_052"},
//    {.desc = "plc_soe_053"},
//    {.desc = "plc_soe_054"},
//    {.desc = "plc_soe_055"},
//    {.desc = "plc_soe_056"},
//    {.desc = "plc_soe_057"},
//    {.desc = "plc_soe_058"},
//    {.desc = "plc_soe_059"},
//    {.desc = "plc_soe_060"},
//    {.desc = "plc_soe_061"},
//    {.desc = "plc_soe_062"},
//    {.desc = "plc_soe_063"},
//    {.desc = "plc_soe_064"},
//    {.desc = "plc_soe_065"},
//    {.desc = "plc_soe_066"},
//    {.desc = "plc_soe_067"},
//    {.desc = "plc_soe_068"},
//    {.desc = "plc_soe_069"},
//    {.desc = "plc_soe_070"},
//    {.desc = "plc_soe_071"},
//    {.desc = "plc_soe_072"},
//    {.desc = "plc_soe_073"},
//    {.desc = "plc_soe_074"},
//    {.desc = "plc_soe_075"},
//    {.desc = "plc_soe_076"},
//    {.desc = "plc_soe_077"},
//    {.desc = "plc_soe_078"},
//    {.desc = "plc_soe_079"},
//    {.desc = "plc_soe_080"},
//    {.desc = "plc_soe_081"},
//    {.desc = "plc_soe_082"},
//    {.desc = "plc_soe_083"},
//    {.desc = "plc_soe_084"},
//    {.desc = "plc_soe_085"},
//    {.desc = "plc_soe_086"},
//    {.desc = "plc_soe_087"},
//    {.desc = "plc_soe_088"},
//    {.desc = "plc_soe_089"},
//    {.desc = "plc_soe_090"},
//    {.desc = "plc_soe_091"},
//    {.desc = "plc_soe_092"},
//    {.desc = "plc_soe_093"},
//    {.desc = "plc_soe_094"},
//    {.desc = "plc_soe_095"},
//    {.desc = "plc_soe_096"},
//    {.desc = "plc_soe_097"},
//    {.desc = "plc_soe_098"},
//    {.desc = "plc_soe_099"},
//    {.desc = "plc_soe_100"},
//    {.desc = "plc_soe_101"},
//    {.desc = "plc_soe_102"},
//    {.desc = "plc_soe_103"},
//    {.desc = "plc_soe_104"},
//    {.desc = "plc_soe_105"},
//    {.desc = "plc_soe_106"},
//    {.desc = "plc_soe_107"},
//    {.desc = "plc_soe_108"},
//    {.desc = "plc_soe_109"},
//    {.desc = "plc_soe_110"},
//    {.desc = "plc_soe_111"},
//    {.desc = "plc_soe_112"},
//    {.desc = "plc_soe_113"},
//    {.desc = "plc_soe_114"},
//    {.desc = "plc_soe_115"},
//    {.desc = "plc_soe_116"},
//    {.desc = "plc_soe_117"},
//    {.desc = "plc_soe_118"},
//    {.desc = "plc_soe_119"},
//    {.desc = "plc_soe_120"},
//    {.desc = "plc_soe_121"},
//    {.desc = "plc_soe_122"},
//    {.desc = "plc_soe_123"},
//    {.desc = "plc_soe_124"},
//    {.desc = "plc_soe_125"},
//    {.desc = "plc_soe_126"},
//    {.desc = "plc_soe_127"},
//    {.desc = "plc_soe_128"}
};

s_plcsoeRefTabItem g_plcalmsoeRefTab[] = {
    {.desc = "plc_alm_soe_001"},
    {.desc = "plc_alm_soe_002"},
    {.desc = "plc_alm_soe_003"},
    {.desc = "plc_alm_soe_004"},
    {.desc = "plc_alm_soe_005"},
    {.desc = "plc_alm_soe_006"},
    {.desc = "plc_alm_soe_007"},
    {.desc = "plc_alm_soe_008"},
    {.desc = "plc_alm_soe_009"},
    {.desc = "plc_alm_soe_010"},
    {.desc = "plc_alm_soe_011"},
    {.desc = "plc_alm_soe_012"},
    {.desc = "plc_alm_soe_013"},
    {.desc = "plc_alm_soe_014"},
    {.desc = "plc_alm_soe_015"},
    {.desc = "plc_alm_soe_016"},
    {.desc = "plc_alm_soe_017"},
    {.desc = "plc_alm_soe_018"},
    {.desc = "plc_alm_soe_019"},
    {.desc = "plc_alm_soe_020"},
    {.desc = "plc_alm_soe_021"},
    {.desc = "plc_alm_soe_022"},
    {.desc = "plc_alm_soe_023"},
    {.desc = "plc_alm_soe_024"},
    {.desc = "plc_alm_soe_025"},
    {.desc = "plc_alm_soe_026"},
    {.desc = "plc_alm_soe_027"},
    {.desc = "plc_alm_soe_028"},
    {.desc = "plc_alm_soe_029"},
    {.desc = "plc_alm_soe_030"},
    {.desc = "plc_alm_soe_031"},
    {.desc = "plc_alm_soe_032"},
    {.desc = "plc_alm_soe_033"},
    {.desc = "plc_alm_soe_034"},
    {.desc = "plc_alm_soe_035"},
    {.desc = "plc_alm_soe_036"},
    {.desc = "plc_alm_soe_037"},
    {.desc = "plc_alm_soe_038"},
    {.desc = "plc_alm_soe_039"},
    {.desc = "plc_alm_soe_040"},
    {.desc = "plc_alm_soe_041"},
    {.desc = "plc_alm_soe_042"},
    {.desc = "plc_alm_soe_043"},
    {.desc = "plc_alm_soe_044"},
    {.desc = "plc_alm_soe_045"},
    {.desc = "plc_alm_soe_046"},
    {.desc = "plc_alm_soe_047"},
    {.desc = "plc_alm_soe_048"},
    {.desc = "plc_alm_soe_049"},
    {.desc = "plc_alm_soe_050"},
    {.desc = "plc_alm_soe_051"},
    {.desc = "plc_alm_soe_052"},
    {.desc = "plc_alm_soe_053"},
    {.desc = "plc_alm_soe_054"},
    {.desc = "plc_alm_soe_055"},
    {.desc = "plc_alm_soe_056"},
    {.desc = "plc_alm_soe_057"},
    {.desc = "plc_alm_soe_058"},
    {.desc = "plc_alm_soe_059"},
    {.desc = "plc_alm_soe_060"},
    {.desc = "plc_alm_soe_061"},
    {.desc = "plc_alm_soe_062"},
    {.desc = "plc_alm_soe_063"},
    {.desc = "plc_alm_soe_064"},
    {.desc = "plc_alm_soe_065"},
    {.desc = "plc_alm_soe_066"},
    {.desc = "plc_alm_soe_067"},
    {.desc = "plc_alm_soe_068"},
    {.desc = "plc_alm_soe_069"},
    {.desc = "plc_alm_soe_070"},
    {.desc = "plc_alm_soe_071"},
    {.desc = "plc_alm_soe_072"},
    {.desc = "plc_alm_soe_073"},
    {.desc = "plc_alm_soe_074"},
    {.desc = "plc_alm_soe_075"},
    {.desc = "plc_alm_soe_076"},
    {.desc = "plc_alm_soe_077"},
    {.desc = "plc_alm_soe_078"},
    {.desc = "plc_alm_soe_079"},
    {.desc = "plc_alm_soe_080"},
    {.desc = "plc_alm_soe_081"},
    {.desc = "plc_alm_soe_082"},
    {.desc = "plc_alm_soe_083"},
    {.desc = "plc_alm_soe_084"},
    {.desc = "plc_alm_soe_085"},
    {.desc = "plc_alm_soe_086"},
    {.desc = "plc_alm_soe_087"},
    {.desc = "plc_alm_soe_088"},
    {.desc = "plc_alm_soe_089"},
    {.desc = "plc_alm_soe_090"},
    {.desc = "plc_alm_soe_091"},
    {.desc = "plc_alm_soe_092"},
    {.desc = "plc_alm_soe_093"},
    {.desc = "plc_alm_soe_094"},
    {.desc = "plc_alm_soe_095"},
    {.desc = "plc_alm_soe_096"},
    {.desc = "plc_alm_soe_097"},
    {.desc = "plc_alm_soe_098"},
    {.desc = "plc_alm_soe_099"},
    {.desc = "plc_alm_soe_100"},
//    {.desc = "plc_alm_soe_101"},
//    {.desc = "plc_alm_soe_102"},
//    {.desc = "plc_alm_soe_103"},
//    {.desc = "plc_alm_soe_104"},
//    {.desc = "plc_alm_soe_105"},
//    {.desc = "plc_alm_soe_106"},
//    {.desc = "plc_alm_soe_107"},
//    {.desc = "plc_alm_soe_108"},
//    {.desc = "plc_alm_soe_109"},
//    {.desc = "plc_alm_soe_110"},
//    {.desc = "plc_alm_soe_111"},
//    {.desc = "plc_alm_soe_112"},
//    {.desc = "plc_alm_soe_113"},
//    {.desc = "plc_alm_soe_114"},
//    {.desc = "plc_alm_soe_115"},
//    {.desc = "plc_alm_soe_116"},
//    {.desc = "plc_alm_soe_117"},
//    {.desc = "plc_alm_soe_118"},
//    {.desc = "plc_alm_soe_119"},
//    {.desc = "plc_alm_soe_120"},
//    {.desc = "plc_alm_soe_121"},
//    {.desc = "plc_alm_soe_122"},
//    {.desc = "plc_alm_soe_123"},
//    {.desc = "plc_alm_soe_124"},
//    {.desc = "plc_alm_soe_125"},
//    {.desc = "plc_alm_soe_126"},
//    {.desc = "plc_alm_soe_127"},
//    {.desc = "plc_alm_soe_128"}
};

int g_plcsetRefTabSize;
int g_plcsoeRefTabSize;
int g_plcsoealmRefTabSize;

int getplcsoealmTabNums()
{
	return g_plcsoealmRefTabSize;
}

int getplcsoeTabNums()
{
	return g_plcsoeRefTabSize;
}

int getplcsetTabNums()
{
	return g_plcsetRefTabSize;
}

/**
 * @brief 获取PLC数字量引用表大小
 */
int getplcBiSize(void) {
    return sizeof(g_BIplcRefTab) / sizeof(s_plcRefTab);
}

/**
 * @brief 获取PLC模拟量引用表大小
 */
int getplcAnaSize(void) {
    return sizeof(g_ANAplcRefTab) / sizeof(s_plcRefTab);
}

/**
 * @brief 初始化PLC引用表 - 分别初始化数字量表和模拟量表
 */
int initPlcRefTab(void) {
    int i;
    int biRefTabSize = getplcBiSize();
    int anaRefTabSize = getplcAnaSize();

    // 初始化遥控
    initPlcCtl();

    // 初始化数字量引用表中的ID字段
    for (i = 0; i < biRefTabSize; i++) {
        if (g_BIplcRefTab[i].desc != NULL) {
            // 数字量数据 - 使用103协议的getBiIdByDesc函数
            g_BIplcRefTab[i].id = getBiIdByDesc(g_BIplcRefTab[i].desc);
            
            // 初始化变化检测相关字段
            g_BIplcRefTab[i].oldVal = 0;
            g_BIplcRefTab[i].changeFlg = 0;
        }
    }

    // 初始化模拟量引用表中的ID字段
    for (i = 0; i < anaRefTabSize; i++) {
        if (g_ANAplcRefTab[i].desc != NULL) {
            // 模拟量数据 - 使用103协议的getAnaIdByDesc函数
            g_ANAplcRefTab[i].id = getAnaIdByDesc(g_ANAplcRefTab[i].desc);
            
            // 初始化变化检测相关字段
            g_ANAplcRefTab[i].oldVal = 0;
            g_ANAplcRefTab[i].changeFlg = 0;
        }
    }

    return SUCC;
}

/**
 * @brief 检查PLC数字量数据变化
 */
void checkPlcBiDataChange(void) {
    int i;
    int refTabSize = getplcBiSize();
    char currentVal;

    for (i = 0; i < refTabSize; i++) {
        if (g_BIplcRefTab[i].desc != NULL && (g_BIplcRefTab[i].plcIoType == PLC_INPUT || g_BIplcRefTab[i].plcIoType == PLC_MIDDLE )) {
            // 获取当前值 - 使用103协议的数据获取函数
            currentVal = getBiById(g_BIplcRefTab[i].id);

            // 检查是否有变化 - 参照103协议的变化检测逻辑
            if (currentVal != g_BIplcRefTab[i].oldVal) {
                g_BIplcRefTab[i].changeFlg = 1;
                g_BIplcRefTab[i].oldVal = currentVal;
            }
        }
    }
}

/**
 * @brief 检查PLC模拟量数据变化
 */
void checkPlcAnaDataChange(void) {
    int i;
    int refTabSize = getplcAnaSize();
    int currentVal;

    for (i = 0; i < refTabSize; i++) {
        if (g_ANAplcRefTab[i].desc != NULL && (g_ANAplcRefTab[i].plcIoType == PLC_INPUT || g_ANAplcRefTab[i].plcIoType == PLC_MIDDLE )) {
            // 获取当前值 - 使用103协议的数据获取函数
            currentVal = getAnaById(g_ANAplcRefTab[i].id);

            // 检查是否有变化 - 参照103协议的变化检测逻辑
            if (currentVal != g_ANAplcRefTab[i].oldVal) {
                g_ANAplcRefTab[i].changeFlg = 1;
                g_ANAplcRefTab[i].oldVal = currentVal;
            }
        }
    }
}

/**
 * @brief 同步数字量数据到PLC
 * @param id 数据ID
 * @return 成功返回SUCC，失败返回FAIL
 */
int syncBiDataToPlc(int id) {
    // 通过ID查找对应的数字量引用表项
    int refTabSize = getplcBiSize();
    const s_plcRefTab* item = NULL;

    for (int i = 0; i < refTabSize; i++) {
        if (g_BIplcRefTab[i].id == id) {
            item = &g_BIplcRefTab[i];
            break;
        }
    }

    if (item == NULL) {
        return FAIL;  // 未找到对应的引用表项
    }

    // 数字量数据 - 使用PLC库的set_digital_data接口
    bool value = getBiById(item->id);

    return g_plcApi->set_digital_data(item->plcVarTypeChar, item->plcAddrA, item->plcAddrB, value);
}

/**
 * @brief 同步所有变化的数字量数据到PLC
 */
int syncAllChangedBiDataToPlc(void) {
    int i;
    int refTabSize = getplcBiSize();
    int syncCount = 0;

    for (i = 0; i < refTabSize; i++) {
        s_plcRefTab* item = &g_BIplcRefTab[i];

        if (item->plcIoType == PLC_OUTPUT) {
            continue;
        }

        // 检查是否有变化
       // if (item->changeFlg) {
        if (1) {
            if (syncBiDataToPlc(item->id) == SUCC) {
                item->changeFlg = 0;  // 清除变化标志
                syncCount++;
            }
            else
            {
                //deviceBs(PLC_RUN_ERR);
            }
        }
    }

    return syncCount;
}

/**
 * @brief 同步模拟量数据到PLC
 * @param id 数据ID
 * @return 成功返回SUCC，失败返回FAIL
 */
int syncAnaDataToPlc(int id) {
    // 通过ID查找对应的模拟量引用表项
    int refTabSize = getplcAnaSize();
    const s_plcRefTab* item = NULL;

    for (int i = 0; i < refTabSize; i++) {
        if (g_ANAplcRefTab[i].id == id) {
            item = &g_ANAplcRefTab[i];
            break;
        }
    }

    if (item == NULL) {
        return FAIL;  // 未找到对应的引用表项
    }

    // 模拟量数据 - 使用PLC库的set_analog_data接口
    float value = (float)getAnaById(item->id);
    char point = getAnaPointById(item->id);
    while (point > 0) {
        value /= 10.0f;
        point--;
    }

    return g_plcApi->set_analog_data(item->plcVarTypeChar, item->plcAddrA, (uint32_t)value);//TODO
}

/**
 * @brief 同步所有变化的模拟量数据到PLC
 */
int syncAllChangedAnaDataToPlc(void) {
    int i;
    int refTabSize = getplcAnaSize();
    int syncCount = 0;

    for (i = 0; i < refTabSize; i++) {
        s_plcRefTab* item = &g_ANAplcRefTab[i];

        if (item->plcIoType == PLC_OUTPUT) {
            continue;
        }

        // 检查是否有变化
       // if (item->changeFlg) {
        if (1) {
            if (syncAnaDataToPlc(item->id) == SUCC) {
                item->changeFlg = 0;  // 清除变化标志
                syncCount++;
            }
            else
            {
                //deviceBs(PLC_RUN_ERR);
            }
        }
    }

    return syncCount;
}

/**
 * @brief 从PLC同步数字量数据
 * @param id 数据ID
 * @param plc_value 输出参数，返回从PLC读取的值
 * @return 成功返回SUCC，失败返回FAIL
 */
int syncBiDataFromPlc(int id, bool* plc_value) {
    // 通过ID查找对应的数字量引用表项
    int refTabSize = getplcBiSize();
    const s_plcRefTab* item = NULL;

    for (int i = 0; i < refTabSize; i++) {
        if (g_BIplcRefTab[i].id == id) {
            item = &g_BIplcRefTab[i];
            break;
        }
    }

    if (item == NULL) {
        return FAIL;  // 未找到对应的引用表项
    }

    // 数字量数据 - 使用PLC库的get_digital_data接口
    bool value;
    int result = g_plcApi->get_digital_data(item->plcVarTypeChar, item->plcAddrA, item->plcAddrB, &value);
    if (result == PLC_OK) {
        setBiById(item->id, (char)value);
        if (plc_value != NULL) {
            *plc_value = value;  // 返回从PLC读取的值
        }
    }

    return result;
}

/**
 * @brief 从PLC同步所有数字量输出数据
 */
int syncAllBiOutputDataFromPlc(void) {
    int i;
    int refTabSize = getplcBiSize();
    int syncCount = 0;
    bool plc_value;
    unsigned char has_output = 0;  // 标记是否有非零输出

    for (i = 0; i < refTabSize; i++) {
        const s_plcRefTab* item = &g_BIplcRefTab[i];

        // 只处理PLC输出类型的数据
        if (item->plcIoType == PLC_INPUT) {
            continue;
        }

        if (syncBiDataFromPlc(item->id, &plc_value) == SUCC) {
            syncCount++;
            // 检查是否有非零输出
            if (plc_value != 0) {
                has_output = 1;
            }
        }
        else
        {
            //deviceBs(PLC_RUN_ERR);//TODO
        }
    }

    // 更新全局变量
    g_plc_output_active = has_output;

    return syncCount;
}

/**
 * @brief 从PLC同步模拟量数据
 * @param id 数据ID
 * @return 成功返回SUCC，失败返回FAIL
 */
int syncAnaDataFromPlc(int id) {
    // 通过ID查找对应的模拟量引用表项
    int refTabSize = getplcAnaSize();
    const s_plcRefTab* item = NULL;

    for (int i = 0; i < refTabSize; i++) {
        if (g_ANAplcRefTab[i].id == id) {
            item = &g_ANAplcRefTab[i];
            break;
        }
    }

    if (item == NULL) {
        return FAIL;  // 未找到对应的引用表项
    }

    // 模拟量数据 - 使用PLC库的get_analog_data接口
    uint32_t value;
    int result = g_plcApi->get_analog_data(item->plcVarTypeChar, item->plcAddrA, &value);
    if (result == PLC_OK) {
        // 模出备用
        //setAnaById(item->id, (int)value);
    }

    return result;
}

/**
 * @brief 从PLC同步所有模拟量输出数据
 */
int syncAllAnaOutputDataFromPlc(void) {
    int i;
    int refTabSize = getplcAnaSize();
    int syncCount = 0;

    for (i = 0; i < refTabSize; i++) {
        const s_plcRefTab* item = &g_ANAplcRefTab[i];

        if (item->plcIoType != PLC_OUTPUT) {
            continue;
        }

        if (syncAnaDataFromPlc(item->id) == SUCC) {
            syncCount++;
        }
        else
        {
            //deviceBs(PLC_RUN_ERR);
        }
    }

    return syncCount;
}


/**************************************  PLC定值同步相关接口   *************************************/

/**
 * @brief 初始化PLC定值 - 从PLC获取定值信息并存储到定值系统
 * @return 成功返回SUCC，失败返回FAIL
 */
int initPlcSettings(void) {
    int i;

    // 获取PLC定值个数
    g_plcsetRefTabSize = g_plcApi->get_const_value_count();

    // 限制最大定值数量为128个
    if (g_plcsetRefTabSize > CONST_VALUE_MAX_NUM) {
        g_plcsetRefTabSize = CONST_VALUE_MAX_NUM;
        return FAIL;
    }

    // 遍历所有PLC定值
    for (i = 0; i < g_plcsetRefTabSize; i++) {
        // 获取单个定值信息
        if (g_plcApi->get_const_value(i, &(g_allSetplcRefTab[i].cv)) != PLC_OK) {
            continue;  // 获取失败，跳过此定值
        }

        // 使用addSetByDesc函数将定值信息存储到g_setDataSheet
        // 映射关系：cv.addr -> plc_addr, cv.value -> defaultValue, cv.addr -> name.showName
        addSetByDesc(
            g_allSetplcRefTab[i].desc,                      // desc - 定值描述名称
            (int)g_allSetplcRefTab[i].cv.min,          // min - 最小值
            (int)g_allSetplcRefTab[i].cv.max,          // max - 最大值
            (int)g_allSetplcRefTab[i].cv.value,  // defaultValue - 默认值来自cv.value
            (char)g_allSetplcRefTab[i].cv.decimal_places, // point - 小数点位数
            NULL,                                   // unit - 单位（暂设为NULL）
            g_allSetplcRefTab[i].cv.addr,           // plc_addr - PLC地址来自cv.addr
            g_allSetplcRefTab[i].cv.desc          // showName - 显示名称来自cv.desc
        );
    }

    return SUCC;
}

/**
 * @brief 从本机传到PLC - 将本机定值同步到PLC的Q区
 * @return 成功返回SUCC，失败返回FAIL
 */
int syncPlcSettingsToPlc(void)
{
	int i;
	int plcSetCount;
	int setValue;
	unsigned char plcAddr;
	int result;
	int successCount = 0;
	int failCount = 0;

	// 获取PLC定值个数
	plcSetCount = getplcsetTabNums();
	if (plcSetCount <= 0) {
		return FAIL;
	}

	// 循环处理每个PLC定值
	for (i = 0; i < plcSetCount; i++) {

		// 通过getSetByDesc获得对应描述的值
		setValue = getSetByDesc(g_allSetplcRefTab[i].desc);//TODO
		if (setValue == 0) {
			// 定值不存在或值为0，跳过
			continue;
		}

		// 通过getSetplcaddr获得对应的地址
		plcAddr = getSetplcaddr(g_allSetplcRefTab[i].desc);
		if (plcAddr == 0) {
			// 地址无效，跳过
			failCount++;
			continue;
		}

		// 通过g_plcApi->set_analog_data接口写入M区
		result = g_plcApi->set_analog_data('M', plcAddr, (uint32_t)setValue);//TODO
		if (result == PLC_OK) {
			successCount++;
		} else {
			failCount++;
		}
	}

	// 如果有成功的操作，返回成功；否则返回失败
	return (successCount > 0) ? SUCC : FAIL;
}

/**************************************  遥控   *************************************/

//遥控
int initPlcCtl(void) {
    regBI(0,"plc_var_001",0,&g_plc_ctl[0]);
    regBI(0,"plc_var_002",0,&g_plc_ctl[1]);
    regBI(0,"plc_var_003",0,&g_plc_ctl[2]);
    regBI(0,"plc_var_004",0,&g_plc_ctl[3]);
    regBI(0,"plc_var_005",0,&g_plc_ctl[4]);
    regBI(0,"plc_var_006",0,&g_plc_ctl[5]);
    regBI(0,"plc_var_007",0,&g_plc_ctl[6]);
    regBI(0,"plc_var_008",0,&g_plc_ctl[7]);
    regBI(0,"plc_var_009",0,&g_plc_ctl[8]);
    regBI(0,"plc_var_010",0,&g_plc_ctl[9]);
    regBI(0,"plc_var_011",0,&g_plc_ctl[10]);
    regBI(0,"plc_var_012",0,&g_plc_ctl[11]);
    regBI(0,"plc_var_013",0,&g_plc_ctl[12]);
    regBI(0,"plc_var_014",0,&g_plc_ctl[13]);
    regBI(0,"plc_var_015",0,&g_plc_ctl[14]);
    regBI(0,"plc_var_016",0,&g_plc_ctl[15]);
    regBI(0,"plc_var_017",0,&g_plc_ctl[16]);
    regBI(0,"plc_var_018",0,&g_plc_ctl[17]);
    regBI(0,"plc_var_019",0,&g_plc_ctl[18]);
    regBI(0,"plc_var_020",0,&g_plc_ctl[19]);
    regBI(0,"plc_var_021",0,&g_plc_ctl[20]);
    regBI(0,"plc_var_022",0,&g_plc_ctl[21]);
    regBI(0,"plc_var_023",0,&g_plc_ctl[22]);
    regBI(0,"plc_var_024",0,&g_plc_ctl[23]);
    regBI(0,"plc_var_025",0,&g_plc_ctl[24]);
    regBI(0,"plc_var_026",0,&g_plc_ctl[25]);
    regBI(0,"plc_var_027",0,&g_plc_ctl[26]);
    regBI(0,"plc_var_028",0,&g_plc_ctl[27]);
    regBI(0,"plc_var_029",0,&g_plc_ctl[28]);
    regBI(0,"plc_var_030",0,&g_plc_ctl[29]);
    regBI(0,"plc_var_031",0,&g_plc_ctl[30]);
    regBI(0,"plc_var_032",0,&g_plc_ctl[31]);
    regBI(0,"plc_var_033",0,&g_plc_ctl[32]);
    regBI(0,"plc_var_034",0,&g_plc_ctl[33]);
    regBI(0,"plc_var_035",0,&g_plc_ctl[34]);
    regBI(0,"plc_var_036",0,&g_plc_ctl[35]);
    regBI(0,"plc_var_037",0,&g_plc_ctl[36]);
    regBI(0,"plc_var_038",0,&g_plc_ctl[37]);
    regBI(0,"plc_var_039",0,&g_plc_ctl[38]);
    regBI(0,"plc_var_040",0,&g_plc_ctl[39]);
    regBI(0,"plc_var_041",0,&g_plc_ctl[40]);
    regBI(0,"plc_var_042",0,&g_plc_ctl[41]);
    regBI(0,"plc_var_043",0,&g_plc_ctl[42]);
    regBI(0,"plc_var_044",0,&g_plc_ctl[43]);
    regBI(0,"plc_var_045",0,&g_plc_ctl[44]);
    regBI(0,"plc_var_046",0,&g_plc_ctl[45]);
    regBI(0,"plc_var_047",0,&g_plc_ctl[46]);
    regBI(0,"plc_var_048",0,&g_plc_ctl[47]);
    regBI(0,"plc_var_049",0,&g_plc_ctl[48]);
    regBI(0,"plc_var_050",0,&g_plc_ctl[49]);
    regBI(0,"plc_var_051",0,&g_plc_ctl[50]);
    regBI(0,"plc_var_052",0,&g_plc_ctl[51]);
    regBI(0,"plc_var_053",0,&g_plc_ctl[52]);
    regBI(0,"plc_var_054",0,&g_plc_ctl[53]);
    regBI(0,"plc_var_055",0,&g_plc_ctl[54]);
    regBI(0,"plc_var_056",0,&g_plc_ctl[55]);
    regBI(0,"plc_var_057",0,&g_plc_ctl[56]);
    regBI(0,"plc_var_058",0,&g_plc_ctl[57]);
    regBI(0,"plc_var_059",0,&g_plc_ctl[58]);
    regBI(0,"plc_var_060",0,&g_plc_ctl[59]);
    regBI(0,"plc_var_061",0,&g_plc_ctl[60]);
    regBI(0,"plc_var_062",0,&g_plc_ctl[61]);
    regBI(0,"plc_var_063",0,&g_plc_ctl[62]);
    regBI(0,"plc_var_064",0,&g_plc_ctl[63]);
}


/**************************************  PLC SOE读取相关接口   *************************************/

/**
 * @brief 初始化PLC SOE - 从PLC获取SOE信息并注册到BI系统
 * @return 成功返回SUCC，失败返回FAIL
 */
int initPlcSoe(void) {
    single_backend_variable_t backendVariable;
    int i;
    int actionSoeCount = 0;  // 普通SOE计数器
    int alarmSoeCount = 0;   // 告警SOE计数器
    int plc_var2Count = 0;   // 计数器

    // 获取PLC后台变量（SOE）个数
    int totalSoeCount = g_plcApi->get_backend_variable_count();

    // 限制最大SOE数量
    if (totalSoeCount > BACKEND_VARIABLE_MAX_NUM) {
        totalSoeCount = BACKEND_VARIABLE_MAX_NUM;
        return FAIL;
    }

    // 遍历所有PLC后台变量
    for (i = 0; i < totalSoeCount; i++) {
        // 获取单个后台变量信息
        if (g_plcApi->get_backend_variable(i, &backendVariable) != PLC_OK) {
            continue;  // 获取失败，跳过此变量
        }

        // 根据变量类型进行不同处理
        if (backendVariable.variable_type == ACTION_TYPE) {
            if(actionSoeCount<28)
            {
                // 更新g_plcsoeRefTab数组中对应项的bv字段
                g_plcsoeRefTab[actionSoeCount].bv = backendVariable;

                // 使用regBI函数注册普通SOE
                if(g_plcsoeRefTab[actionSoeCount].bv.addr_a>=8 && g_plcsoeRefTab[actionSoeCount].bv.addr_a<=23)
                {
                plc_var2Count = (g_plcsoeRefTab[actionSoeCount].bv.addr_a - 8) * 8 + g_plcsoeRefTab[actionSoeCount].bv.addr_b;
                regBI(0, g_plc_var2RefTab[plc_var2Count].desc, 0, (char*)(&(g_plcsoeRefTab[actionSoeCount].bv.value)));
                regBI(0, g_plcsoeRefTab[plc_var2Count].desc, 0, (char*)(&(g_plcsoeRefTab[actionSoeCount].bv.value)));
                }

                setBiNameByDesc(g_plcsoeRefTab[actionSoeCount].desc, g_plcsoeRefTab[actionSoeCount].bv.desc);
                actionSoeCount++;
            }

        } else if (backendVariable.variable_type == ALARM_TYPE) {
            if(alarmSoeCount<100)
            {
                // 更新g_plcalmsoeRefTab数组中对应项的bv字段
                g_plcalmsoeRefTab[alarmSoeCount].bv = backendVariable;

                // 使用regBI函数注册告警SOE
                if(g_plcalmsoeRefTab[alarmSoeCount].bv.addr_a>=8 && g_plcalmsoeRefTab[alarmSoeCount].bv.addr_a<=23)
                {
                plc_var2Count = (g_plcalmsoeRefTab[alarmSoeCount].bv.addr_a - 8) * 8 + g_plcalmsoeRefTab[alarmSoeCount].bv.addr_b;
                regBI(0, g_plc_var2RefTab[plc_var2Count].desc, 0, (char*)(&(g_plcalmsoeRefTab[alarmSoeCount].bv.value)));
                regBI(0, g_plcalmsoeRefTab[plc_var2Count].desc, 0, (char*)(&(g_plcalmsoeRefTab[alarmSoeCount].bv.value)));
                }

                setBiNameByDesc(g_plcalmsoeRefTab[alarmSoeCount].desc, g_plcalmsoeRefTab[alarmSoeCount].bv.desc);
                alarmSoeCount++;
            }
        }
    }

    // 更新全局计数器
    g_plcsoeRefTabSize = actionSoeCount;
    g_plcsoealmRefTabSize = alarmSoeCount;

    return SUCC;
}

/**
 * @brief 读取SOE变位功能 - 从PLC的M区读取SOE状态并更新到本机
 * @return 成功返回SUCC，失败返回FAIL
 */
int readPlcSoeValues(void)
{
    int i;
    int normalSoeCount;
    int alarmSoeCount;
    bool plcValue;
    int result;
    int successCount = 0;
    int failCount = 0;

    // 1、分别用 getplcsoeTabNums 和 getplcsoealmTabNums 获取 普通SOE个数和告警SOE个数
    normalSoeCount = getplcsoeTabNums();
    alarmSoeCount = getplcsoealmTabNums();

    // 2、循环获取普通SOE的值：使用 get_digital_data 获取，M区，g_plcsoeRefTab.bv.addr_a，g_plcsoeRefTab.bv.addr_b，g_plcsoeRefTab.bv.value获取值
    for (i = 0; i < normalSoeCount; i++) {
        // 使用g_plcApi->get_digital_data接口从M区读取
        result = g_plcApi->get_digital_data('M',
                                          g_plcsoeRefTab[i].bv.addr_a,
                                          g_plcsoeRefTab[i].bv.addr_b,
                                          &plcValue);

        if (result == PLC_OK) {
            // 更新g_plcsoeRefTab[i].bv.value
            g_plcsoeRefTab[i].bv.value = plcValue;
            successCount++;
        } else {
            failCount++;
        }
    }

    // 3、同理获取告警SOE的值：使用 get_digital_data 获取，M区，g_plcalmsoeRefTab.bv.addr_a，g_plcalmsoeRefTab.bv.addr_b，g_plcalmsoeRefTab.bv.value获取值
    for (i = 0; i < alarmSoeCount; i++) {
        // 使用g_plcApi->get_digital_data接口从M区读取
        result = g_plcApi->get_digital_data('M',
                                          g_plcalmsoeRefTab[i].bv.addr_a,
                                          g_plcalmsoeRefTab[i].bv.addr_b,
                                          &plcValue);

        if (result == PLC_OK) {
            // 更新g_plcalmsoeRefTab[i].bv.value
            g_plcalmsoeRefTab[i].bv.value = plcValue;
            successCount++;
        } else {
            failCount++;
        }
    }

    // 如果有成功的操作，返回成功；否则返回失败
    return (successCount > 0) ? SUCC : FAIL;
}

