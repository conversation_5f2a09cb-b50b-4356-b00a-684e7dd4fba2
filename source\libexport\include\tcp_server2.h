
#ifndef __TCP_SERVER2_H__
#define __TCP_SERVER2_H__


#ifdef __cplusplus
extern "C"{
#endif


#include "main.h"
#include "lwip.h"
#include "tcp.h"

// 第二个TCP服务器接口声明
void tcp_server2_init(void);
int get_tcp_server2_data(char *buf, int len);
int send_tcp_server2_data(char *buff_tx, int buff_tx_len);
void send_tcp_server2_data_plc(uint8_t *buff_tx, uint16_t buff_tx_len);

#ifdef __cplusplus
}
#endif

#endif
