#include "plc_comm.h"
#include "tcp_server.h"
//#define PLCDEBUG

//uint32_t MD3;
#ifdef PLCDEBUG
plc_err_t ret;
single_const_value_t cvtest;
single_backend_variable_t bvtest;
single_backend_variable_t bvtest1;
uint32_t MD2;
uint32_t MD21;
bool MD0;
uint32_t count=0;
bool data;
uint32_t LED1 = 1;
#endif

//PLC初始化配置
s_plcInitConfig g_plcInitConfig = {
    //.appSize = 7932,
    .appSize = 0,
    .appMd5 = {0x4C,0xE5,0x8B,0xFA,0x80,0xDA,0x00,0xA1,0x3B,0x8F,0x5B,0xB7,0xA4,0x59,0xED,0xBC},
    .autoStart = 1,
    .enableTick = 1
};

//PLC API接口指针
plc_api_t* g_plcApi = NULL;

extern s_versionInfo g_version;

/**
 * @brief 初始化PLC通信模块 - 参照103协议的初始化流程
 */
int initPlcComm(void) {
    int result = FAIL;

    // 获取PLC API接口指针
    g_plcApi = plc_api_g;
    if (g_plcApi == NULL) {
        return FAIL;
    }

    // 初始化PLC库 - 使用PLC库的plc_init接口
    result = g_plcApi->plc_init(g_plcInitConfig.appSize, g_plcInitConfig.appMd5);
    if (result != PLC_OK) {
        return FAIL;
    }

    result = g_plcApi->get_plc_ver((uint8_t *) (&g_version.plc_ver1), (uint8_t *) &g_version.plc_ver2);
    if (result != PLC_OK) {
        return FAIL;
    }

    // 自动启动PLC
//    if (g_plcInitConfig.autoStart) {
//        // PLC自动启动逻辑，使用PLC库接口
//        result = g_plcApi->set_plc_state(PLC_STATE_START);  // 启动PLC
//        if (result != PLC_OK) {
//            return FAIL;
//        }
//    }

    // 初始化PLC定值 - 从PLC获取定值信息并存储到定值系统
    if(initPlcSettings() != SUCC)
    {
        return FAIL;
    }

    // 初始化PLC SOE - 从PLC获取SOE信息并注册到BI系统
    if(initPlcSoe() != SUCC)
    {
        return FAIL;
    }

#ifdef PLCDEBUG
    count = plc_api_g->get_const_value_count();
    count = plc_api_g->get_backend_variable_count();

    plc_api_g->get_const_value(0,&cvtest);
    plc_api_g->get_backend_variable(0,&bvtest);
    //plc_api_g->get_backend_variable(1,&bvtest1);
#endif

    return SUCC;
}

/**
 * @brief PLC通信主任务 - 参照103协议的主循环处理
 */
void plcCommTask(void) {

    //g_plcApi->get_analog_data('Q', 0, &MD3);
#ifdef PLCDEBUG
    //测试设置开入
    ret = g_plcApi->set_digital_data('I', 0, 0, 1);
    //测试获取开出
    //ret = g_plcApi->get_digital_data('Q', 0, 0, &data);
    //测试设置模入
    //ret = g_plcApi->set_analog_data('M', 0, 1000);
    //测试获取模出
    ret = g_plcApi->get_analog_data('M', 3, &MD3);


    //测试定值获取设置
    ret = g_plcApi->get_analog_data('M', 2, &MD2);
    ret = g_plcApi->set_analog_data('M', 2, 20);
    //测试报告获取
    ret = g_plcApi->get_digital_data('M', 0, 0, &MD0);
    //ret = g_plcApi->set_digital_data('I', 0, 1, 1);

    //测试设置开入
    ret = g_plcApi->set_digital_data('M', 0, 6, 1);

    // 调用PLC任务 - 使用PLC库的plc_task接口
    g_plcApi->plc_task();

    //测试获取开出
    ret = g_plcApi->get_digital_data('Q', 0, 6, &data);
#else

    // 同步定值
    if(syncPlcSettingsToPlc() != SUCC)
    {
        //deviceBs(PLC_RUN_ERR);//TODO
    }

    // 检查数据变化 - 参照103协议的变化检测机制
    checkPlcDataChange();

    // 同步变化的数据到PLC
    syncAllChangedDataToPlc();

    // 调用PLC任务 - 使用PLC库的plc_task接口
    if(g_plcApi->plc_task() != SUCC)
    {
        //deviceBs(PLC_RUN_ERR);
    }

    // 从PLC同步输出数据
    syncAllOutputDataFromPlc();

    // 读取SOE变位
    if(readPlcSoeValues() != SUCC)
    {
        //deviceBs(PLC_RUN_ERR);
    }
#endif

}


unsigned char tem_tcp_buf[1024];
void PLC_test_task(void)
{
    int recvLen;
    recvLen = get_tcp_server_data((char *)tem_tcp_buf, sizeof(tem_tcp_buf));
    if (recvLen > 0)
	{
        g_plcApi->debug_data_handle(tem_tcp_buf,sizeof(tem_tcp_buf), send_tcp_server_data_plc);
	}
}

/**
 * @brief 检查PLC数据变化 - 统一调用数字量和模拟量变化检测
 */
void checkPlcDataChange(void) {
    checkPlcBiDataChange();
    checkPlcAnaDataChange();
}

/**
 * @brief 同步所有变化的数据到PLC - 参照103协议的批量同步机制
 */
int syncAllChangedDataToPlc(void) {
    int biSyncCount = syncAllChangedBiDataToPlc();
    int anaSyncCount = syncAllChangedAnaDataToPlc();
    
    return biSyncCount + anaSyncCount;
}

/**
 * @brief 从PLC同步所有输出数据 - 参照103协议的批量同步机制
 */
int syncAllOutputDataFromPlc(void) {
    int biSyncCount = syncAllBiOutputDataFromPlc();
    int anaSyncCount = syncAllAnaOutputDataFromPlc();
    
    return biSyncCount + anaSyncCount;
}
