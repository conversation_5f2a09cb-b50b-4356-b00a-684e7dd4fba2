#include "umain.h"
#ifndef WIN32
#include  "stm32f4xx_hal_rtc.h"
#include  "stm32f4xx_hal.h"
extern RTC_HandleTypeDef hrtc;
extern uint8_t need_reboot;  // 是否需要重启标志
#endif


/*保护中断任务*/
void task_Fun(void)
{
#ifdef TIME_TEST		
		unsigned int timeCnt;
		timeCnt = g_sys_100usCnt;
#endif
	//装置闭锁直接返回
	if (g_bsFlg != RUNNING_OK)
    {
        runledoff();//NOTEL 陷阱，if语句尽量用{}包起来，不然可能后续加的时候，加了两句，但二句没在条件判断中
        return;
    } else
    {
        runledon();
    }

	runTaskLevel2();//运行队列2，按顺序运行保护功能模块任务

	traveAllRefTab();//告警 动作 引用表，遍历告警动作引用表产生SOE

	check103BiTab();//103开关量变位上传

	checkLbTab();//录波表，遍历录波引用表判断是否录波

	refreshKeepStatus();//事故标志，刷新动作标志
	
	BcodeSetTime();//设置装置B码解析的时间，B码校时判断
	
#ifdef TIME_TEST		
		taskModTime = getIntTime(timeCnt);
#endif		
}

unsigned char  AD_CNT;
unsigned int  AD_Value_SUM[11];
unsigned int  AD_Value_MIN[11];
unsigned int  AD_Value_MAX[11];
//!!! AD定时采样任务
void task_AD(void)
{
	unsigned char i;
#ifdef TIME_TEST		
		unsigned int timeCnt;
		timeCnt = g_sys_100usCnt;
#endif
	
	//采样任务
    AD7616_ISR();
	ReadAD7616FifoToBuffer();	//!!! 从FIFO读取数据到AD_Value数组
#if 0
	AD_CNT++;
	if(AD_CNT == 1)
	{
		for(i = 0; i < 11; i++)
		{
			AD_Value_MIN[i] = AD_Value[i]; //!!! AD_Value[i]为ADC接口
			AD_Value_MAX[i] = AD_Value[i];
		}	
	}
	else
	{
		for(i = 0; i < 11; i++)
		{
			if(AD_Value[i] < AD_Value_MIN[i])
				AD_Value_MIN[i] = AD_Value[i];		
			if(AD_Value[i] > AD_Value_MAX[i])
				AD_Value_MAX[i] = AD_Value[i];
		}
	}
	
	for(i = 0; i < 11; i++)
	{
		AD_Value_SUM[i] += AD_Value[i];
	}
	if(AD_CNT >= 5)
	{
		for(i = 0; i < 11; i++)
		{
			AD_Value_AVG[i] = (AD_Value_SUM[i] - AD_Value_MIN[i] - AD_Value_MAX[i]) / (AD_CNT - 2);
			AD_Value_SUM[i] = 0;
		}		
		AD_CNT = 0;
		sample_task();
	}
	else
	{
		//DONE AD启动下次转换
		AD7616_StartConvst();
	}
#else
		for(i = 0; i < 11; i++)
		{
			AD_Value_AVG[i] = AD_Value[i];
		}		
		sample_task();

		//DONE AD启动下次转换
		AD7616_StartConvst();
#endif

    excitationGetANAData();

#ifdef TIME_TEST		
		taskSmplTime = getIntTime(timeCnt);
#endif		
}

/*500us中断任务*/
unsigned char	g_100usTaskCnt;
unsigned char	g_2msTaskCnt;
unsigned short	lastSec;
//!!! 该任务主要用于系统计时
void task_500us(void)
{
	//g_sys_100usCnt++;
	//g_100usTaskCnt++;
	//BcodeSetTime();	//B码校时判断

	g_sys_100usCnt+=5;
    g_100usTaskCnt+=5;
    g_2msTaskCnt+=1;

	if (g_100usTaskCnt >= 10) //1ms 计时
	{
		g_100usTaskCnt = 0;

        g_s7kmTimer++;      //通信超时定时器，每1ms加1
        g_s7kmRecvTimer++;  //接收超时定时器，每1ms加1
        g_ModbusRunTimer++;

        ki_read();    //1ms读取开关量
        excitationGetStateData(); //1ms读取开关量

        // 调用PLC tick函数（修正语法错误：添加函数调用括号）
        if (plc_api_g != NULL && plc_api_g->plc_tick != NULL)
        {
            plc_api_g->plc_tick();
        }

		//读取时间 判断秒翻转
#ifdef WIN32
		SYSTEMTIME st;
		GetLocalTime(&st);
		if (lastSec != st.wSecond)
		{
			g_sys_ms = 0;
			lastSec = st.wSecond;
		}	
#else
//		RTC_TimeTypeDef RTC_TimeStruct;
//		HAL_RTC_GetTime(&hrtc, &RTC_TimeStruct, RTC_FORMAT_BIN);
//		if (lastSec != RTC_TimeStruct.Seconds)
//		{
//			g_sys_ms = 0;
//			lastSec = RTC_TimeStruct.Seconds;
//		}
#endif
	}

    if (g_2msTaskCnt >= 4) //2ms 计时
    {
        g_2msTaskCnt = 0;

        //装置闭锁直接返回
        if (g_bsFlg != RUNNING_OK)
            return;

        stall_feed();	//翻转stall

    }

}


#ifdef WIN32
/*模拟中断,用来测试采样任务*/
void  timerSample(void * arg)
{
	while (1)
	{
		Sleep(100);			 //100ms
		task_833us();
	}
}

/*模拟中断,用来测试保护任务*/
void  timerProtect(void * arg)
{
	int cnt = 0;

	while (1)
	{
		Sleep(1000);			//1000ms
		task_Fun();
	}
}
#endif

s_versionInfo g_version;

void initversion(void)
{
    g_version.year = 2025;
    g_version.month = 07;
    g_version.day = 15;
    g_version.app_ver1 = 1;
    g_version.app_ver2 = 00;
    //g_version.plc_ver1 = 1;
    //g_version.plc_ver2 = 00;
    g_version.app_crc = 0xFC47;
    regANA("version","year",0,&g_version.year,0,0);
    regANA("version","month",0,&g_version.month,0,0);
    regANA("version","day",0,&g_version.day,0,0);
    regANA("version","app_ver1",0,&g_version.app_ver1,0,0);
    regANA("version","app_ver2",0,&g_version.app_ver2,0,0);
    regANA("version","plc_ver1",0,&g_version.plc_ver1,0,0);
    regANA("version","plc_ver2",0,&g_version.plc_ver2,0,0);
    regANA("version","app_crc",0,&g_version.app_crc,0,0);
}

void umainInit(void)
{
	//extern int TestSignal;
	initversion();

    if(initPlcComm() != SUCC)
    {
        //deviceBs(PLC_RUN_ERR);
    };

    //放在Bi前，SOE数量获取

	/*定值初始化*/
	initSet();

	//注册平台信号
	devSgl_reg();
	
	//regANA(0,"OUTPUT_TEST",0,&TestSignal,0,0);
	
	//模块初始化注册信号
	initModTask();	
	initModParm();

    // 初始化引用表 - 使用103协议的引用表初始化方式
    if (initPlcRefTab() != SUCC) {
        //deviceBs(PLC_RUN_ERR);
    }

    /*初始化ADC*/
	AD7616_GPIO_init();
	bsp_InitAD7616();

	/*采样任务初始化*/
	initSampeTask();

    initSerial7000Master();

	/*初始化引用表*/
	initBiRefTab();
	init103RefTab();

	/*初始化SOE铁电存储信息*/
	InitSoeStore();

	/*初始化事故标志*/
	initKeepStatus();

	/*录波任务*/
	initLBTask();

	/*103通讯初始化*/
	N103Init();
	S103Init();
	ModbusInit();

	//B码初始化
	initBcodeTask();
	
	//清除装置闭锁
	deviceClearBs(INIT_NOT_OK);

    wdg_feed();
	//wdg_enable();//TODO

#ifdef WIN32
	//初始化完成 开启中断任务
	_beginthread(timerSample, 2000, 0);
	_beginthread(timerProtect, 2000, 0);
#endif
}

#ifdef WIN32
void main()
#else
void umain()
#endif
{
	//应用初始化
	umainInit();
	
	//	int disflagm = 1;
	int Key_Code;
//	int disflagm = 0;
//	int Main_Flg = 1;
//	int Where_Flg = 0;

	//主循环任务
	while (1)
	{
		wdg_feed();
#ifdef TIME_TEST
		unsigned int timeCnt;
		timeCnt = g_sys_100usCnt;
#endif
		
#ifndef WIN32
		MX_LWIP_Process();//LWIP协议栈运行任务
#endif

		deviceSgl_task();//平台信号处理

        Serial7000MasterRun();
		S7kmProcessRecvBuf();

        if(getSetByDesc("plc_test") == 0)
        {
		    N103_task();//以太网103通讯任务
        }

		S103_task();//串口103通讯任务
        ModbusProtocolRun();
        plcCommTask();

        if(getSetByDesc("plc_test") != 0)
        {
            PLC_test_task();
        }

		LB_task();//录波任务

		soeStore_task();//SOE铁电存储任务

		bcode_task();//B码解析任务

		keepStatusTask();//动作标志铁电存储任务
		//wdg_feed();
#ifdef TIME_TEST		
		taskMainTime = getIntTime(timeCnt);
#endif		
        //!!! 如果需要重启，等响应帧发送完后再重启
        if(need_reboot) {
            // 确保响应帧已发送完毕
            HAL_Delay(100);  // 延时100ms确保数据发送完成

            // 进行系统重启
            NVIC_SystemReset();
        }
	}
}
