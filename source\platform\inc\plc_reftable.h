#ifndef _PLC_REFTABLE_H_
#define _PLC_REFTABLE_H_

#include "common.h"
#include "bi.h"
#include "ana.h"
#include "rtc_task.h"
#include "setting.h"
#include "control.h"
#include <stdint.h>
#include <stdbool.h>
#include "plc_lib.h"

// PLC I/O区域类型 - 参照PLC库接口设计
typedef enum {
    PLC_INPUT = 0,
    PLC_OUTPUT,
    PLC_MIDDLE,
} e_plcIoType;

// 全局变量声明 - PLC数字量输出状态标志
extern unsigned char g_plc_output_active;

// PLC引用表条目结构 - 完全参照103协议引用表设计
typedef struct {
    char* desc;                     // 主控内部数据描述符 (如"modBi.bi_in01")
    int id;                         // 主控内部数据ID
    e_plcIoType plcIoType;          // PLC I/O区域类型（包含方向信息）
    char plcVarTypeChar;            // PLC变量类型字符 (I/Q/M)
    uint8_t plcAddrA;               // PLC地址参数a (字节地址)
    uint8_t plcAddrB;               // PLC地址参数b (位地址，仅数字量使用)
    int oldVal;                     // 上次值(用于变化检测，模拟量用int，数字量用低位)
    unsigned char changeFlg;        // 变化标志
} s_plcRefTab;

typedef struct {
    char* desc;                     // 主控内部数据描述符 (如"modBi.bi_in01")
    int id;                         // 主控内部数据ID
} s_plcvarRefTab;

/*
表项结构体
*/
typedef struct
{
	char*	desc;			 //名称，用于匹配对象
	single_const_value_t cv; //PLC定值具体内容
}s_plcSetRefTab;

/*
表项结构体
*/
typedef struct
{
	char*	desc;			 //名称，用于匹配对象
	single_backend_variable_t bv; //PLCSOE具体内容
}s_plcsoeRefTabItem;

// 外部引用表声明
extern plc_api_t* g_plcApi;

int getplcBiSize(void);
int getplcAnaSize(void);
extern int getplcsoealmTabNums();
extern int getplcsoeTabNums();
extern int getplcsetTabNums();

// 函数声明

/**
 * @brief 初始化PLC引用表
 * @return 成功返回SUCC，失败返回FAIL
 */
int initPlcRefTab(void);


/**
 * @brief 检查PLC数字量数据变化
 * @return 无返回值
 */
void checkPlcBiDataChange(void);

/**
 * @brief 检查PLC模拟量数据变化
 * @return 无返回值
 */
void checkPlcAnaDataChange(void);

/**
 * @brief 同步数字量数据到PLC
 * @param id 数据ID
 * @return 成功返回SUCC，失败返回FAIL
 */
int syncBiDataToPlc(int id);

/**
 * @brief 同步所有变化的数字量数据到PLC
 * @return 同步的数据项数量
 */
int syncAllChangedBiDataToPlc(void);

/**
 * @brief 同步模拟量数据到PLC
 * @param id 数据ID
 * @return 成功返回SUCC，失败返回FAIL
 */
int syncAnaDataToPlc(int id);

/**
 * @brief 同步所有变化的模拟量数据到PLC
 * @return 同步的数据项数量
 */
int syncAllChangedAnaDataToPlc(void);

/**
 * @brief 从PLC同步数字量数据
 * @param id 数据ID
 * @param plc_value 输出参数，返回从PLC读取的值
 * @return 成功返回SUCC，失败返回FAIL
 */
int syncBiDataFromPlc(int id, bool* plc_value);

/**
 * @brief 从PLC同步所有数字量输出数据
 * @return 同步的数据项数量
 */
int syncAllBiOutputDataFromPlc(void);

/**
 * @brief 从PLC同步模拟量数据
 * @param id 数据ID
 * @return 成功返回SUCC，失败返回FAIL
 */
int syncAnaDataFromPlc(int id);

/**
 * @brief 从PLC同步所有模拟量输出数据
 * @return 同步的数据项数量
 */
int syncAllAnaOutputDataFromPlc(void);


/**
 * @brief 初始化PLC定值 - 从PLC获取定值信息并存储到定值系统
 * @return 成功返回SUCC，失败返回FAIL
 */
int initPlcSettings(void);

int syncPlcSettingsToPlc(void);    // 从本机传到PLC

//遥控
int initPlcCtl(void);

/**
 * @brief 初始化PLC SOE - 从PLC获取SOE信息并注册到BI系统
 * @return 成功返回SUCC，失败返回FAIL
 */
int initPlcSoe(void);

int readPlcSoeValues(void);        // 读取SOE变位功能

#endif // _PLC_REFTABLE_H_
