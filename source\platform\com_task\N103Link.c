#include "N103Slave.h"
#include "xsj_lib.h"
#include "w25qxx.h"
#include "upgrade_flag.h" // 添加升级标志区头文件
#ifdef WIN32
SOCKET IEC103TCP;	//服务器socket
SOCKET IEC103UDP;
#else

#include "udp_server.h"
#include "udp_client.h"
#include "tcp_server.h"
#include "tcp_server2.h"
#include "tcp_client.h"

#endif
s_N103Link g_N103Link[N103_MAX_NUM];
unsigned char	g_N103UdpBuf[41];

#ifdef WIN32
/*打印缓冲区*/
void BufferPrint(unsigned char *buf,  int len)
{
	int i = 0;

	for (i = 0; i < len; i++)
	{
		if ((i % 16) == 0)
		{
			printf("\n");
		}
		printf("%c", buf[i]);
	}
	printf("\n");
}
#endif

char N103TCPInit(void)
{
#ifdef WIN32
	SOCKADDR_IN bindInfo;
	int imode = 1;


	WSADATA data;

	if (WSAStartup(MAKEWORD(2, 2), &data))
	{
		return 0;
	}

	//创建socket
	IEC103TCP = socket(AF_INET, SOCK_STREAM, 0);
	if (IEC103TCP == INVALID_SOCKET)
		return FAIL;

	//bind
	memset(&bindInfo, 0, sizeof(SOCKADDR_IN));
	bindInfo.sin_family = AF_INET;
	bindInfo.sin_addr.S_un.S_addr = INADDR_ANY;
	bindInfo.sin_port = htons(2404);
	if (bind(IEC103TCP, &bindInfo, sizeof(SOCKADDR_IN)) == SOCKET_ERROR)
	{
		return FAIL;
	}

	//lishen
	if (listen(IEC103TCP, 1) == SOCKET_ERROR)
	{
		return FAIL;
	}

	//非阻塞模式
	ioctlsocket(IEC103TCP, FIONBIO, &imode);
#else
	tcp_server_init();   // 初始化第一个TCP服务器 (端口2404)
	tcp_server2_init();  // 初始化第二个TCP服务器 (端口2405)
#endif
	return SUCC;
}

char N103UDPInit(void)
{
#ifdef WIN32
	SOCKADDR_IN bindInfo;

	//创建socket
	IEC103UDP = socket(AF_INET, SOCK_DGRAM, 0);
	if (IEC103UDP == INVALID_SOCKET)
		return FAIL;

	//bind
	memset(&bindInfo, 0, sizeof(SOCKADDR_IN));
	bindInfo.sin_family = AF_INET;
	bindInfo.sin_addr.S_un.S_addr = INADDR_ANY;
	bindInfo.sin_port = htons(1032);
	if (bind(IEC103UDP, &bindInfo, sizeof(SOCKADDR_IN)) == SOCKET_ERROR)
	{
		return FAIL;
	}
#else
	
	udp_init();
	udp_server_init();

#endif
}

/*以太网103初始化*/
char N103Init( void )
{
	int ip; 
	int ipaddr1,ipaddr2,ipaddr3,ipaddr4;
	int mask;
	int getWay;
	
	//ip   = getSetByDesc("Eth1Ip");
    ipaddr1   = getSetByDesc("Eth1Ipaddr1");
    ipaddr2   = getSetByDesc("Eth1Ipaddr2");
    ipaddr3   = getSetByDesc("Eth1Ipaddr3");
    ipaddr4   = getSetByDesc("Eth1Ipaddr4");
    ip = (ipaddr4 << 24) | (ipaddr3 << 16) | (ipaddr2 << 8) | ipaddr1;

    //0x1F42A8C0
	mask = getSetByDesc("Eth1Mask");
    //修正网关地址计算逻辑：保持网络部分，主机位设为1
	getWay = (ip & 0x00FFFFFF) | 0x01000000;
	//getWay = 0x0142A8C0;
#ifndef WIN32
	net_param_confige((unsigned char*)&ip, (unsigned char*)&mask, (unsigned char*)&getWay); //!!! 端口ip设置
#endif
	N103TCPInit();
	N103UDPInit();	
	
    IEC103_init_varible();

	return SUCC;
}

void N103_linkInit(int clientNo)
{
	int i;

	g_N103Link[clientNo].waitAckFlg = 0;

	g_N103Link[clientNo].addr = getSetByDesc("devic_addr");
	g_N103Link[clientNo].AnaUpTime = getSetByDesc("IEC103_AnaUp_t") * 10;	//以100us为单位
	g_N103Link[clientNo].curOpSetNo = 1;

	g_N103Link[clientNo].lastRespAckNo = 0;
	g_N103Link[clientNo].lastAckNo = 0;
	g_N103Link[clientNo].recNo = 0;
	g_N103Link[clientNo].senNo = 0;

	g_N103Link[clientNo].lastAckTick = g_sys_100usCnt;

	g_N103Link[clientNo].rWPtr = 0;
	g_N103Link[clientNo].sWPtr = 0;
	g_N103Link[clientNo].sRPtr = 0;

	for (i = 0; i < PRI_NUMS; i++)
	{
		memset(&g_N103Link[clientNo].app[i], 0, sizeof(s_N103App));
	}
}

/*UDP校时任务*/
void N103UDPProc(void)
{


	int recvLen;
	s_date time;

	#ifdef WIN32
	if (IEC103UDP == INVALID_SOCKET)
		return;

	recvLen = recv(IEC103UDP,g_N103UdpBuf,41,0);
	#else
	recvLen = get_udp_server_data((char*)g_N103UdpBuf, sizeof(g_N103UdpBuf));
	#endif
	
	if (recvLen != 41)
		return;

	//如有B码则不采用PMC网络效时
	if (g_devState & DEV_BCODE)
		return;
	
	//分析校时命令
	if (g_N103UdpBuf[0] != 0xFF && g_N103UdpBuf[1] != 0x01)
		return;

	//时间转换
	recvLen = g_N103UdpBuf[2] + (g_N103UdpBuf[3] << 8);
	time.ms = recvLen % 1000;
	recvLen /= 1000;
	time.s = recvLen;					//秒
	time.min = g_N103UdpBuf[4];			//分
	time.hour = g_N103UdpBuf[5];		//时
	time.day = g_N103UdpBuf[6] & 0x1f;	//日
	time.month = g_N103UdpBuf[7];		//月
	time.year = 2000 + g_N103UdpBuf[8];	//年

	//校时
	setCurDate(&time);
}

void N103_closeLink(int clientNo)
{
	#ifdef WIN32
	closesocket(g_N103Link[clientNo].TCPfd);
	g_N103Link[clientNo].TCPfd = INVALID_SOCKET;
	#endif
	g_N103Link[clientNo].linkflg = 0;
	N103_linkInit(clientNo);
}

/*建立新链接*/
void N103_link(void)
{
	int i;
	#ifdef WIN32
	SOCKET newN103TcpFd;

	//accept
	newN103TcpFd = accept(IEC103TCP, 0, 0);
	if (newN103TcpFd == INVALID_SOCKET)
		return;

	//分配链接
	for (i = 0; i < N103_MAX_NUM; i++)
	{
		if (!(g_N103Link[i].linkflg))
		{
			break;
		}
	}

	if (i >= N103_MAX_NUM)
	{
		closesocket(newN103TcpFd);
		return;
	}

	//链接管理结构初始化
	g_N103Link[i].linkflg = 1;
	g_N103Link[i].TCPfd = newN103TcpFd;
	N103_linkInit(i);

	//指示应用层链路复位
	N103_Indicate(i, 0);
	
	#else
	
	if(get_network_staus())	//网络在线
	{
		for (i = 0; i < N103_MAX_NUM; i++)
		{
			if(g_N103Link[i].linkflg == 0)
			{
				N103_linkInit(i);	
				N103_Indicate(i, 0);
			}		
		}
	}
	else
	{
		for (i = 0; i < N103_MAX_NUM; i++)
			N103_closeLink(i);
	}
	
	#endif
}

/*接收数据*/
void N103_recvData(int clientNo)
{
	int recvLen;
	
	#ifdef WIN32
	recvLen = recv(g_N103Link[clientNo].TCPfd, g_N103Link[clientNo].recvBuf + g_N103Link[clientNo].rWPtr, RECV_LEN - g_N103Link[clientNo].rWPtr,0);
	#else
	// 根据clientNo选择对应的TCP服务器
	if (clientNo == 0) {
		recvLen = get_tcp_server_data((char*)(g_N103Link[clientNo].recvBuf + g_N103Link[clientNo].rWPtr), RECV_LEN - g_N103Link[clientNo].rWPtr);
	} else if (clientNo == 1) {
		recvLen = get_tcp_server2_data((char*)(g_N103Link[clientNo].recvBuf + g_N103Link[clientNo].rWPtr), RECV_LEN - g_N103Link[clientNo].rWPtr);
	}
	#endif
	
	if (recvLen > 0)
	{
		g_N103Link[clientNo].rWPtr += recvLen;
	}
}

/*发送数据*/
void N103_sendData(int clientNo)
{
	
	int sendLen;

	if (g_N103Link[clientNo].sWPtr <= g_N103Link[clientNo].sRPtr)	//没有需要发送的数据
	{
		g_N103Link[clientNo].sWPtr = 0;
		g_N103Link[clientNo].sRPtr = 0;
	}
	#ifdef WIN32
	sendLen = send(g_N103Link[clientNo].TCPfd, &g_N103Link[clientNo].sendBuf[g_N103Link[clientNo].sRPtr], g_N103Link[clientNo].sWPtr - g_N103Link[clientNo].sRPtr,0);

	if (sendLen >= 0)	//发送成功了
	{
		g_N103Link[clientNo].sRPtr += sendLen;
		if (g_N103Link[clientNo].sRPtr == g_N103Link[clientNo].sWPtr)
		{
			g_N103Link[clientNo].sWPtr = 0;
			g_N103Link[clientNo].sRPtr = 0;
			g_N103Link[clientNo].lastAckTick = g_sys_100usCnt;
		}
	}
	else
	{
		N103_closeLink(clientNo);
	}
	#else
	// 根据clientNo选择对应的TCP服务器
	if (clientNo == 0) {
		sendLen = send_tcp_server_data((char*)&g_N103Link[clientNo].sendBuf[g_N103Link[clientNo].sRPtr], g_N103Link[clientNo].sWPtr - g_N103Link[clientNo].sRPtr);
	} else if (clientNo == 1) {
		sendLen = send_tcp_server2_data((char*)&g_N103Link[clientNo].sendBuf[g_N103Link[clientNo].sRPtr], g_N103Link[clientNo].sWPtr - g_N103Link[clientNo].sRPtr);
	}
	if (sendLen == 0)	//发送成功了
	{
		//g_N103Link[clientNo].sRPtr += sendLen;
		//if (g_N103Link[clientNo].sRPtr == g_N103Link[clientNo].sWPtr)
		{
			g_N103Link[clientNo].sWPtr = 0;
			g_N103Link[clientNo].sRPtr = 0;
			g_N103Link[clientNo].lastAckTick = g_sys_100usCnt;
		}
	}	
	#endif
}

/*发送S帧*/
int N103_SendSFrame(int clientNo)
{
	int i;

	i = g_N103Link[clientNo].sWPtr;
	if ((i + 6) >= SEND_LEN)	//发送缓冲区长度不够，发送失败
		return FAIL;

	g_N103Link[clientNo].sendBuf[i++] = 0x68;
	g_N103Link[clientNo].sendBuf[i++] = 4;
	g_N103Link[clientNo].sendBuf[i++] = 0x01;	
	g_N103Link[clientNo].sendBuf[i++] = 0;
	g_N103Link[clientNo].sendBuf[i++] = g_N103Link[clientNo].recNo&0xff;
	g_N103Link[clientNo].sendBuf[i++] = g_N103Link[clientNo].recNo>>8;

	g_N103Link[clientNo].sWPtr += 6;
	return SUCC;
}


/*发送U帧*/
int N103_SendUFrame(int clientNo, unsigned char cFrameType)
{
	int i;

	i = g_N103Link[clientNo].sWPtr;
	if((i + 6) >= SEND_LEN)	//发送缓冲区长度不够，发送失败
		return FAIL;	

	g_N103Link[clientNo].sendBuf[i++] = 0x68;
	g_N103Link[clientNo].sendBuf[i++] = 4;
	g_N103Link[clientNo].sendBuf[i++] = 0x0003 | cFrameType;	//发送序号
	g_N103Link[clientNo].sendBuf[i++] = 0;
	g_N103Link[clientNo].sendBuf[i++] = 0;
	g_N103Link[clientNo].sendBuf[i++] = 0;

	g_N103Link[clientNo].sWPtr += 6;
	return SUCC;
}

/*解析接收到的数据*/
void N103_AnalyzeRecData(int clientNo)
{
	unsigned int i = 0;
	int recNo;
	int sendNo;
	unsigned char cFrameType;

	if (g_N103Link[clientNo].rWPtr < 6)			//数据太少直接退出
		return;

	while (g_N103Link[clientNo].recvBuf[i] != 0x68 && i < g_N103Link[clientNo].rWPtr)	//找到帧头 0x68
		i++;

	if (i >= g_N103Link[clientNo].rWPtr)			//接收到的全是无用数据 丢弃
	{
		g_N103Link[clientNo].rWPtr = 0;
		return;
	}
	//判断帧是否完整
	if ((g_N103Link[clientNo].rWPtr - i < 6) || ((g_N103Link[clientNo].rWPtr - i) < (g_N103Link[clientNo].recvBuf[i + 1] + 2)))
	{
		if (i > 0)
		{
			memmove(g_N103Link[clientNo].recvBuf, &g_N103Link[clientNo].recvBuf[i], g_N103Link[clientNo].rWPtr - i);
			g_N103Link[clientNo].rWPtr -= i;
		}
		return;
	}
	
	//置链接标志
	g_N103Link[i].linkflg = 1;
	
	//检查完毕 处理完整帧
	cFrameType = g_N103Link[clientNo].recvBuf[i + 2] & 0x03;
	if ((cFrameType & 0x01) == 0) //I帧
	{
		sendNo = g_N103Link[clientNo].recvBuf[i + 2] + (g_N103Link[clientNo].recvBuf[i + 3] << 8);	//对方的发送序号
		recNo = g_N103Link[clientNo].recvBuf[i + 4] + (g_N103Link[clientNo].recvBuf[i + 5] << 8);	//对方的接收序号
		if (g_N103Link[clientNo].recNo != sendNo)
		{
			g_N103Link[clientNo].recNo = sendNo;
			g_N103Link[clientNo].senNo = recNo;
		}
		g_N103Link[clientNo].recNo = sendNo + 2;
		g_N103Link[clientNo].lastRespAckNo = recNo;

		N103_ExplainASDU(clientNo, &g_N103Link[clientNo].recvBuf[i + 6], g_N103Link[clientNo].recvBuf[i + 1] - 4);	//解析ASDU
		i += g_N103Link[clientNo].recvBuf[i + 1] + 2;
	}
	else if (cFrameType == 1) //S帧
	{
		g_N103Link[clientNo].lastRespAckNo = (g_N103Link[clientNo].recvBuf[i+4] + (g_N103Link[clientNo].recvBuf[i + 5] << 8));
		i += 6;
	}
	else //U帧 
	{
		switch (g_N103Link[clientNo].recvBuf[i + 2])
		{
		case 0x83:	//TESTFR 确认			
			break;
		case 0x43:	//TESTFR 生效
			N103_SendUFrame(clientNo, TESTFR_CON);
			break;
		case 0x23:	//STOPDT 确认,不应该出现
			break;
		case 0x13:	//STOPDT 生效
			N103_SendUFrame(clientNo, STOPDT_CON);
			break;
		case 0x0C:	//STARTDT 确认,不应该出现
			break;
		case 0x07:	//STARTDT 生效
			N103_SendUFrame(clientNo, STARTDT_CON);
			break;
		default:
			break;
		}
		i += 6;
	}

	//丢弃处理了的数据
	if (i < g_N103Link[clientNo].rWPtr)
	{
		memmove(g_N103Link[clientNo].recvBuf, &g_N103Link[clientNo].recvBuf[i], g_N103Link[clientNo].rWPtr - i);
	}
	g_N103Link[clientNo].rWPtr -= i;
}

void N103_overTimer(int clientNo)
{
	unsigned int ackTick;

	//S帧发送处理
	if (g_N103Link[clientNo].recNo > (g_N103Link[clientNo].lastAckNo + NET103_MAX_DELAYASK_NUM))
	{
		g_N103Link[clientNo].lastAckNo = g_N103Link[clientNo].recNo;
		N103_SendSFrame(clientNo);
		return;
	}

	//判断无数据报文确认超时 10s
	ackTick = getIntTime(g_N103Link[clientNo].lastAckTick);
	if ((g_N103Link[clientNo].recNo != g_N103Link[clientNo].lastAckNo) && (ackTick > 100000))
	{
		g_N103Link[clientNo].lastAckNo = g_N103Link[clientNo].recNo;
		N103_SendSFrame(clientNo);
		return;
	}

	//判断没有被确认的I格式的APDU最大数目是否超过了设定值 
	if (g_N103Link[clientNo].senNo > (g_N103Link[clientNo].lastRespAckNo + NET103_MAX_SEND_REC_DIFF))
	{
		//暂停发送I格式帧
		g_N103Link[clientNo].waitAckFlg = 1;
	}
	else
	{
		g_N103Link[clientNo].waitAckFlg = 0;
	}

	//15S 没有成功发送了 关闭端口
	if(ackTick > 150000)
	{
		N103_closeLink(clientNo);
		return;
	}
}

/*以太网103任务*/
void N103_task(void)
{
	int i;
	char linkFlg = 0;

	N103UDPProc();
	N103_link();	//链接处理

	for (i = 0; i < N103_MAX_NUM; i++)
	{
		//if (g_N103Link[i].linkflg)
		{
			N103_recvData(i);
			N103_AnalyzeRecData(i);
			if (g_N103Link[i].waitAckFlg == 0 && g_N103Link[i].sWPtr == 0)
			{
				N103_AppLayerRun(i);
			}
			N103_sendData(i);
			N103_overTimer(i);
			if(g_N103Link[i].linkflg)
				linkFlg = 1;
			else
				clearUpFlg(N103_CLIENT_BASE + i);
		}
	}
	
	if(linkFlg)
		deviceState(DEV_N103);		//置以太网通讯标志
	else
		deviceClearState(DEV_N103);				//清以太网通讯标志
}
